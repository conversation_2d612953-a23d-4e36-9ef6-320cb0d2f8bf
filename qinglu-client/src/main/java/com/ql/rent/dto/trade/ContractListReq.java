package com.ql.rent.dto.trade;

import lombok.Data;

import java.util.Date;

/**
 * 合同列表查询请求DTO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class ContractListReq {

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 车辆ID
     */
    private String vehicleId;

    /**
     * 合同状态
     */
    private Integer status;

    /**
     * 合同开始时间范围-开始
     */
    private Date startBeginTime;

    /**
     * 合同开始时间范围-结束
     */
    private Date endBeginTime;

    /**
     * 合同结束时间范围-开始
     */
    private Date startEndTime;

    /**
     * 合同结束时间范围-结束
     */
    private Date endEndTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;
}
