package com.ql.rent.vo.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手动上报合同响应
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel("手动上报合同响应")
public class SubmitContractResponse {

    @ApiModelProperty(value = "三方合同ID", example = "CT202401150001")
    private String contractId;

    @ApiModelProperty(value = "合同编号", example = "HT202401150001")
    private String contractNo;

    @ApiModelProperty(value = "合同状态", example = "1", notes = "-2计划上报 -1表示上报失败 0表示上报中，1表示已上报，2已终止, 3已取消")
    private Integer status;

    @ApiModelProperty(value = "响应码", example = "0000")
    private String postCode;
}
