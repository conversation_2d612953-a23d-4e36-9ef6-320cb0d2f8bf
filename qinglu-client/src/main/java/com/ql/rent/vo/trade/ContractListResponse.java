package com.ql.rent.vo.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 合同列表响应
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel("合同列表响应")
public class ContractListResponse {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "车辆ID")
    private Long vehicleId;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @ApiModelProperty(value = "合同状态")
    private Integer status;

    @ApiModelProperty(value = "状态消息")
    private String message;

    @ApiModelProperty(value = "驾驶员姓名")
    private String driverName;

    @ApiModelProperty(value = "驾驶员电话")
    private String driverPhone;

    @ApiModelProperty(value = "驾驶员身份证")
    private String driverId;

    @ApiModelProperty(value = "122账号")
    private String account;

    @ApiModelProperty(value = "车牌号")
    private String carNumber;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "商家ID")
    private Long merchantId;

    @ApiModelProperty(value = "车辆类型")
    private String carType;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    @ApiModelProperty(value = "最后版本")
    private String lastVer;

    @ApiModelProperty(value = "操作用户ID")
    private Long opUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "操作时间")
    private Date opTime;
}
