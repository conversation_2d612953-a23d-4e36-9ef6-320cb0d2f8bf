package com.ql.rent.vo.trade;

import com.ql.rent.vo.vehicle.VehicleInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 检查车辆改排后库存冲突响应
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel("检查车辆改排后库存冲突响应")
public class VehicleConflictCheckResponse {

    @ApiModelProperty(value = "是否存在冲突", example = "false")
    private Boolean hasConflict;

    @ApiModelProperty(value = "目标车辆信息")
    private VehicleInfoVO targetVehicleInfo;
}
