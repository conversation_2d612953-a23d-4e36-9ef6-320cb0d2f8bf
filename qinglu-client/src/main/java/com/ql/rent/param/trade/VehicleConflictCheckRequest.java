package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 检查车辆改排后库存冲突请求参数
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel("检查车辆改排后库存冲突请求参数")
public class VehicleConflictCheckRequest {

    @ApiModelProperty(value = "订单ID", required = true, example = "12345")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty(value = "目标车牌", required = true, example = "京A12345")
    @NotBlank(message = "目标车牌不能为空")
    private String targetLicense;
}
