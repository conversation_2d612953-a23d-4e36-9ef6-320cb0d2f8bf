package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 查询合同列表请求参数
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel("查询合同列表请求参数")
public class ContractListRequest {

    @ApiModelProperty(value = "商家ID", required = true, example = "1001")
    @NotNull(message = "商家ID不能为空")
    private Long merchantId;

    @ApiModelProperty(value = "订单ID（可选）", example = "12345")
    private Long orderId;

    @ApiModelProperty(value = "车辆ID（可选）", example = "67891")
    private Long vehicleId;

    @ApiModelProperty(value = "合同状态（可选）", example = "1", notes = "-2计划上报 -1表示上报失败 0表示上报中，1表示已上报，2已终止, 3已取消")
    private Integer status;

    @ApiModelProperty(value = "合同开始时间范围-开始（可选）", example = "2024-01-01 00:00:00")
    private Date startBeginTime;

    @ApiModelProperty(value = "合同开始时间范围-结束（可选）", example = "2024-01-31 23:59:59")
    private Date endBeginTime;

    @ApiModelProperty(value = "合同结束时间范围-开始（可选）", example = "2024-01-01 00:00:00")
    private Date startEndTime;

    @ApiModelProperty(value = "合同结束时间范围-结束（可选）", example = "2024-01-31 23:59:59")
    private Date endEndTime;

    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize = 20;
}
