package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 手动上报合同请求参数
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel("手动上报合同请求参数")
public class SubmitContractRequest {

    @ApiModelProperty(value = "订单ID", required = true, example = "12345")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty(value = "合同开始时间", required = true, example = "2024-01-15 10:00:00")
    @NotNull(message = "合同开始时间不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "合同结束时间", required = true, example = "2024-01-20 18:00:00")
    @NotNull(message = "合同结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "车辆ID（可选，不提供则使用订单默认车辆）", example = "67891")
    private Long vehicleId;
}
