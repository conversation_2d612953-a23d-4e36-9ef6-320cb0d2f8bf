package com.ql.rent.provider.pay;

import com.ql.dto.pay.*;
import com.ql.rent.param.merchant.RoyaltyRelationParam;
import com.ql.rent.param.pay.*;
import org.jetbrains.annotations.NotNull;

/**
 * 三方支付处理类
 * <AUTHOR>
 */
public interface ThirdPayHandler {


    @NotNull
    ThirdPayResult createThirdPayOrder(ThirdPayCreateParam thirdPayCreateParam);


    @NotNull
    RefundResult refund(RefundParam refundParam);


    default void postToRefund(PostRefundParam postRefundParam) {return ;};

    /**
     * 分账关系绑定
     * @return
     */
    @NotNull
    default RoyaltyResult royaltyBind(RoyaltyRelationParam royaltyRelationParam) {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 分账关系解绑
     * @return
     */
    @NotNull
    default RoyaltyResult royaltyUnbind(RoyaltyRelationParam royaltyRelationParam) {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 交易结算(分账)
     * @return
     */
    @NotNull
    default OrderSettleResult orderSettle(Long merchantId, OrderSettleParam orderSettleParam) {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 关闭交易单
     * @return
     */
    @NotNull
    default ClosePayResult closePay(ClosePayParam closePayParam) {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 查询分账比例
     */
    @NotNull
    default void royaltyRateQuery() {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 统一收单交易查询
     */
    @NotNull
    default TradeQueryResult tradeQuery(TradeQueryParam param) {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 统一收单交易退款查询
     */
    @NotNull
    default RefundQueryResult refundQuery(RefundQueryParam param) {throw new UnsupportedOperationException("不支持该方法");}

    /**
     * 返回对应的三方支付平台类型
     */
    @NotNull
    Integer thirdPaySource();
}
