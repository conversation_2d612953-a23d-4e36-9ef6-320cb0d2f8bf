package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.common.FileUploader;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.OssComponent;
import com.ql.rent.component.RemoteViolationService;
import com.ql.rent.constant.MallServiceItemConstant;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dao.slave.trade.OrderInfoSlaveMapper;
import com.ql.rent.dao.trade.*;
import com.ql.rent.dto.trade.IllegalExtraDTO;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.WxMpSubscribeTemplateEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.merchant.SysUserQuery;
import com.ql.rent.param.trade.*;
import com.ql.rent.param.vehicle.VehicleInfoQueryParam;
import com.ql.rent.remote.vehicle.dto.VehicleIllegalRecordDTO;
import com.ql.rent.remote.vehicle.enums.licenseTypeEnum;
import com.ql.rent.remote.vehicle.vo.response.VehicleIllegalResponse;
import com.ql.rent.service.common.IAreaService;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.merchant.IMerchantItemUsageService;
import com.ql.rent.service.merchant.SysUserService;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.trade.*;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.AreaVo;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.login.WxSubscribeMsgVo;
import com.ql.rent.vo.merchant.MallServiceItemUsageDTO;
import com.ql.rent.vo.merchant.SysUserVo;
import com.ql.rent.vo.store.StoreInfoVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @time 2023-12-18 14:19
 * @Version 1.0
 */
@Service
@Slf4j
public class VehicleIllegalSearchServiceImpl implements IVehicleIllegalSearchService {
    @Resource
    private VehicleIllegalSettingMapper vehicleIllegalSettingMapper;
    @Resource
    private VehicleIllegalSettingDetailMapper vehicleIllegalSettingDetailMapper;
    @Resource
    private VehicleIllegalRecordMapper vehicleIllegalRecordMapper;
    @Resource
    private VehicleIllegalRecordDetailMapper vehicleIllegalRecordDetailMapper;
    @Resource
    private RemoteViolationService remoteViolationService;
    @Resource
    private IRedisService redisService;
    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;
    @Resource
    private IVehiclePickReturnService vehiclePickReturnService;
    @Resource
    private IAreaService areaService;
    @Resource
    private IMallServiceOrderInfoService mallServiceOrderInfoService;
    @Resource
    private IMerchantItemUsageService merchantItemUsage;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IVehicleIllegalSearchService vehicleIllegalSearchService;
    @Resource
    private IPushMsgService pushMsgService;
    @Resource
    private IStoreInfoService storeInfoService;
    @Resource
    private IOrderSlaveService orderSlaveService;
    @Resource
    private OrderInfoSlaveMapper orderInfoSlaveMapper;
    @Resource
    private OssComponent ossComponent;
    @Resource
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;
    @Resource
    private ITransferContractStatusService transferContractStatusService;

    @Override
    public Result<VehicleIllegalSearchVO> listIllegalSearchAndSave(List<VehicleIllegalSearchParam> vehicleIllegalSearchParamList, Long merchantId, Long opUserId) {
        if (CollectionUtils.isEmpty(vehicleIllegalSearchParamList) || merchantId == null || opUserId == null) {
            return ResultUtil.failResult("参数不能为空");
        }
        log.info("违章查询 merchantId={}, opUserId={}, vehicleIllegalSearchParamList={}", merchantId, opUserId, JSON.toJSONString(vehicleIllegalSearchParamList));
        String redisKey = String.format("vehicleIllegalSearch:%s", merchantId);
        long localTime = System.currentTimeMillis();

        long check = redisService.setnx(redisKey, 90);
        if (check > 1) {
            return ResultUtil.failResult("请勿频繁查询违章");
        }

        MallServiceItemUsageDTO mallServiceItemUsageDTO;
        try {
            // 查询正在使用的订单
            Result<MallServiceItemUsageDTO> mallServiceItemUsageDTOResult =
                    merchantItemUsage.mallServiceItemUsage(merchantId, MallOrderItemEnum.ILLEGAL_SEARCH.getItemType());
            if (ResultUtil.isModelNull(mallServiceItemUsageDTOResult)) {
                return ResultUtil.failResult("请先选择使用的套餐");
            }

            // 当前订单DTO
            mallServiceItemUsageDTO = mallServiceItemUsageDTOResult.getModel();
            // 成功次数
            int successCount = 0;
            // 失败次数
            int failCount = 0;
            // 剩余次数
            int remainingCount = 0;
            // 当前使用的套餐类型
            String usageOrderItem;
            // 违章查询订单
            MallServiceOrderInfoDTO searchServiceOrderInfo = null;
            // 违章转移订单List
            List<MallServiceOrderInfoDTO> transferServiceOrderList = null;
            // 违章转移 车辆绑定map
            Map<Long, MallServiceOrderInfoDTO> vehicleBindMap = new HashMap<>();
            // 订单id
            Long mallServiceOrderInfoId = null;
            // 订单号
            String mallServiceOrderInfoOrderNo = "";

            if (mallServiceItemUsageDTO.getItem() != null){
                usageOrderItem = MallServiceItemConstant.ILLEGAL.SERVICE;
                searchServiceOrderInfo = mallServiceItemUsageDTO.getItem();

                final Integer originRemainingCount = searchServiceOrderInfo.getRemainingCount();
                remainingCount = searchServiceOrderInfo.getRemainingCount();

                if (originRemainingCount < vehicleIllegalSearchParamList.size() || searchServiceOrderInfo.getExpirationDate().before(new Date())) {
                    if (originRemainingCount < vehicleIllegalSearchParamList.size()) {
                        redisService.set(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId),
                                IllegalSearchProgressVO.builder()
                                        .progress(0.0)
                                        .failReason("套餐次数不足以完成本次查询")
                                        .build(),
                                5 * 60);
                    } else {
                        redisService.set(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId),
                                IllegalSearchProgressVO.builder()
                                        .progress(0.0)
                                        .failReason("套餐已过期")
                                        .build(),
                                5 * 60);
                    }
                    return ResultUtil.successResult(VehicleIllegalSearchVO.builder()
                            .result(false)
                            .expirationDate(searchServiceOrderInfo.getExpirationDate())
                            .vehicleCount(vehicleIllegalSearchParamList.size())
                            .RemainingCount(originRemainingCount).build());
                }
                mallServiceOrderInfoId = searchServiceOrderInfo.getId();
                mallServiceOrderInfoOrderNo = searchServiceOrderInfo.getOrderNo();
            } else if (CollectionUtils.isNotEmpty(mallServiceItemUsageDTO.getIllTranItem())) {
                // 违章转移套餐 只要选中一个所有违章转移套餐都可以用
                usageOrderItem = MallServiceItemConstant.ILLEGAL.TRANSFER;
                transferServiceOrderList = mallServiceItemUsageDTO.getIllTranItem();
                Map<Long, MallServiceOrderInfoDTO> orderMap = transferServiceOrderList.stream()
                        .filter(e -> e.getExpirationDate().after(new Date()))
                        .collect(Collectors.toMap(MallServiceOrderInfoDTO::getId, e -> e, (k1, k2) -> k1));
                if (orderMap.isEmpty()) {
                    redisService.set(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId),
                            IllegalSearchProgressVO.builder()
                                    .progress(0.0)
                                    .failReason("套餐已过期")
                                    .build(),
                            5 * 60);
                    return ResultUtil.failResult("所有违章转移套餐皆已过期");
                }

                // 查询订单对应的车辆
                TransferVehicleBindQuery query = new TransferVehicleBindQuery();
                query.setMerchantId(merchantId);
                query.setOrderIds(new ArrayList<>(orderMap.keySet()));
                // key：vehicleId value:orderId
                vehicleIllegalTransferDetailService.listTransferVehicleBind(query).getModel().forEach(e -> {
                    if (orderMap.get(e.getOrderId()) != null) {
                        vehicleBindMap.put(e.getVehicleId(), orderMap.get(e.getOrderId()));
                    }
                });
                mallServiceOrderInfoId = transferServiceOrderList.get(0).getId();
                mallServiceOrderInfoOrderNo = transferServiceOrderList.get(0).getOrderNo();
            } else {
                return ResultUtil.failResult("请先选择使用的套餐");
            }

            // 保存初始化消耗记录主表
            VehicleIllegalRecord vehicleIllegalRecord = new VehicleIllegalRecord();
            vehicleIllegalRecord.setSearchCount(vehicleIllegalSearchParamList.size());
            vehicleIllegalRecord.setSuccessCount(0);
            vehicleIllegalRecord.setFailCount(0);
            vehicleIllegalRecord.setRemainingCount(remainingCount);
            vehicleIllegalRecord.setServiceOrderId(mallServiceOrderInfoId);
            vehicleIllegalRecord.setServiceOrderNo(mallServiceOrderInfoOrderNo);
            vehicleIllegalRecord.setMerchantId(merchantId);
            vehicleIllegalRecord.setDeleted(YesOrNoEnum.NO.getValue());
            vehicleIllegalRecord.setCreateTime(localTime);
            vehicleIllegalRecord.setOpUserId(opUserId);
            vehicleIllegalRecordMapper.insertSelective(vehicleIllegalRecord);

            for (int i = 0; i < vehicleIllegalSearchParamList.size(); i++) {
                VehicleIllegalSearchParam illegalSearchParam = vehicleIllegalSearchParamList.get(i);

                // 运行进度条
                redisService.set(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId),
                        IllegalSearchProgressVO.builder()
                                .progress((double) (i + 1) / vehicleIllegalSearchParamList.size())
                                .build(),
                        5 * 60);
                String licenseNo = illegalSearchParam.getLicenseNo();
                Long vehicleId = illegalSearchParam.getVehicleId();

                // 如果是违章转移套餐 更新车辆对应的订单
                if (Objects.equals(usageOrderItem, MallServiceItemConstant.ILLEGAL.TRANSFER)) {
                    mallServiceOrderInfoId = vehicleBindMap.get(vehicleId).getId();
                    mallServiceOrderInfoOrderNo = vehicleBindMap.get(vehicleId).getOrderNo();
                }

                //先check车辆
                Result<VehicleInfoVO> vehicleResult = vehicleInfoService.getBaseById(vehicleId, false);
                if (ResultUtil.isModelNull(vehicleResult)
                        || !vehicleResult.getModel().getMerchantId().equals(merchantId)
                        || !vehicleResult.getModel().getLicense().equals(licenseNo)) {
                    continue;
                }

                //车牌号7位就是普通汽车，车牌号8位就是新能源,
                String carType = getCarType(licenseNo);
                Result<VehicleIllegalResponse> vehicleIllegal = remoteViolationService.getVehicleIllegal(licenseNo, carType);
                log.info("违章查询,merchantId={},licenseNo={},carType={},vehicleIllegal={}", merchantId
                        , licenseNo, carType, JSON.toJSONString(vehicleIllegal));

                // 构造消耗详细
                VehicleIllegalRecordDetail vehicleIllegalRecordDetail = new VehicleIllegalRecordDetail();
                vehicleIllegalRecordDetail.setVehicleId(vehicleId);
                vehicleIllegalRecordDetail.setLicenseNo(licenseNo);
                vehicleIllegalRecordDetail.setServiceOrderId(mallServiceOrderInfoId);
                vehicleIllegalRecordDetail.setServiceOrderNo(mallServiceOrderInfoOrderNo);
                if (vehicleIllegal.isSuccess()) {
                    successCount++;
                    if (Objects.equals(usageOrderItem, MallServiceItemConstant.ILLEGAL.SERVICE)) {
                        remainingCount--;
                    }
                    vehicleIllegalRecordDetail.setRemainingCount(remainingCount);
                    vehicleIllegalRecordDetail.setStatus(YesOrNoEnum.YES.getValue());
                    vehicleIllegalRecordDetail.setFailReason("");
                } else {
                    failCount++;
                    vehicleIllegalRecordDetail.setRemainingCount(remainingCount);
                    vehicleIllegalRecordDetail.setStatus(YesOrNoEnum.NO.getValue());
                    vehicleIllegalRecordDetail.setFailReason(vehicleIllegal.getMessage());
                }
                vehicleIllegalRecordDetail.setMerchantId(merchantId);
                vehicleIllegalRecordDetail.setDeleted(YesOrNoEnum.NO.getValue());
                vehicleIllegalRecordDetail.setCreateTime(localTime);
                vehicleIllegalRecordDetail.setOpUserId(opUserId);
                vehicleIllegalRecordDetail.setIllegalRecordId(vehicleIllegalRecord.getId());
                // 更新消耗记录主表
                VehicleIllegalRecord newRecord = new VehicleIllegalRecord();
                newRecord.setId(vehicleIllegalRecord.getId());
                newRecord.setSuccessCount(successCount);
                newRecord.setFailCount(failCount);
                newRecord.setRemainingCount(remainingCount);
                vehicleIllegalRecordMapper.updateByPrimaryKeySelective(newRecord);
                // 保存消耗记录详细
                vehicleIllegalRecordDetailMapper.insert(vehicleIllegalRecordDetail);

                if (ResultUtil.isResultNotSuccess(vehicleIllegal)) {
                    continue;
                }
                VehicleIllegalResponse vehicleIllegalResponse = vehicleIllegal.getModel();
                // 违章查询套餐 根据订单id，减少1次剩余次数
                if (Objects.equals(usageOrderItem, MallServiceItemConstant.ILLEGAL.SERVICE)) {
                    mallServiceOrderInfoService.updateMallOrderInfo(mallServiceOrderInfoId, 1);
                }
                // 保存违章数据 (不做处理违章)
                try {
                    if (vehicleIllegalResponse.getHasData() && CollectionUtils.isNotEmpty(vehicleIllegalResponse.getRecords())) {
                        for (VehicleIllegalRecordDTO illegalRecordDTO : vehicleIllegalResponse.getRecords()) {
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            String archive = illegalRecordDTO.getArchive();
                            Date illegalTime = null;
                            try {
                                illegalTime = dateFormat.parse(illegalRecordDTO.getTime());
                            } catch (ParseException e) {
                                log.error("时间转换失败,merchantId={},vehicleIllegal={}", merchantId, JSON.toJSONString(vehicleIllegal));
                                continue;
                            }

                            // 幂等（查询三方标识+文书编号+车辆id 如没有文书编号，则三方标识+违章时间+车辆id）
                            VehicleIllegalOrderVO vehicleIllegalOrderVO = null;
                            if (StringUtils.isNotEmpty(archive)) {
                                List<VehicleIllegalOrderVO> illegalOrders = vehicleIllegalOrderService.getBaseVehicleIllegalOrder(VehicleIllegalOrderInnerQuery.builder()
                                        .fromSource(IllegalOrderFromSourceEnum.THIRD.getSource())
                                        .archive(archive)
                                        .vehicleId(vehicleId)
                                        .build());
                                if (CollectionUtils.isNotEmpty(illegalOrders)) {
                                    vehicleIllegalOrderVO = illegalOrders.get(0);
                                }
                            } else {
                                List<VehicleIllegalOrderVO> illegalOrders = vehicleIllegalOrderService.getBaseVehicleIllegalOrder(VehicleIllegalOrderInnerQuery.builder()
                                        .fromSource(IllegalOrderFromSourceEnum.THIRD.getSource())
                                        .illegalTime(illegalTime)
                                        .vehicleId(vehicleId)
                                        .build());
                                if (CollectionUtils.isNotEmpty(illegalOrders)) {
                                    vehicleIllegalOrderVO = illegalOrders.get(0);
                                }
                            }

                            // 根据违章时间车辆id匹配订单号
                            Long orderId = 0L;
                            if (vehicleIllegalOrderVO == null || vehicleIllegalOrderVO.getHandleStatus() == 0) {
                                Result<List<Long>> orderIdResult = vehiclePickReturnService.getOrderIdByTimeV2(vehicleId, illegalTime, merchantId);
                                if (ResultUtil.isModelNotNull(orderIdResult)) {
                                    orderId = orderIdResult.getModel().get(0);
                                }
                            } else {
                                // 单据处理了也要set订单id，后续需要订单id匹配合同
                                orderId = vehicleIllegalOrderVO.getOrderId();
                            }

                            // 违章城市id
                            Long cityId = null;
                            if (StringUtils.isNotEmpty(archive) && archive.length() >= 4) {
                                // 匹配城市id
                                String cityCode = archive.substring(0, 4) + "00";
                                Result<AreaVo> voResult = areaService.findByCode(cityCode);
                                if (ResultUtil.isModelNotNull(voResult)) {
                                    cityId = voResult.getModel().getId();
                                }
                                if (cityId == null) {
                                    // 匹配省份id
                                    String provinceCode = archive.substring(0, 2) + "00";
                                    voResult = areaService.findByCode(provinceCode);
                                    if (ResultUtil.isModelNotNull(voResult)) {
                                        cityId = voResult.getModel().getId();
                                    }
                                }
                            }
                            if (cityId == null) {
                                // 匹配车辆所在门店id
                                Result<StoreInfoVo> storeInfoVoResult = storeInfoService.storeInfoBaseFind(vehicleResult.getModel().getStoreId());
                                if (ResultUtil.isModelNotNull(storeInfoVoResult)) {
                                    cityId = storeInfoVoResult.getModel().getCityId();
                                }
                            }
                            if (cityId == null) {
                                // 最终的兜底
                                cityId = 0L;
                            }

                            // 凭证 暂时为null
                            List<VehicleIllegalProofVO> vehicleIllegalProofVOList = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(illegalRecordDTO.getPhotos())) {
                                for (String picUrl : illegalRecordDTO.getPhotos()) {
                                    VehicleIllegalProofVO vehicleIllegalProofVO = new VehicleIllegalProofVO();
                                    vehicleIllegalProofVO.setProofUrl(FileUploader.addFilePrivatePrefix(ossComponent.uploadByUrl(picUrl)));
                                    vehicleIllegalProofVOList.add(vehicleIllegalProofVO);
                                }
                            }

                            VehicleIllegalOrderParam vehicleIllegalOrderParam = VehicleIllegalOrderParam.builder()
                                    .id(vehicleIllegalOrderVO != null ? vehicleIllegalOrderVO.getId() : null)
                                    .orderId(orderId)
                                    .illegalTime(illegalTime)
                                    .illegalCityId(cityId)
                                    .illegalAddr(illegalRecordDTO.getLocation())
                                    .illegalAction(illegalRecordDTO.getReason())
                                    .fraction(illegalRecordDTO.getDegree() != null ? Integer.parseInt(illegalRecordDTO.getDegree()) : 0)
                                    .penaltyAmount(illegalRecordDTO.getFine() != null ? Long.parseLong(illegalRecordDTO.getFine()) * 100 : 0)
                                    .fromSource(IllegalOrderFromSourceEnum.THIRD.getSource())
                                    .vehicleId(vehicleId)
                                    .merchantId(merchantId)
                                    .archive(archive)
                                    .proofList(vehicleIllegalProofVOList)
                                    .realHandleStatus(Byte.valueOf(illegalRecordDTO.getStatus()))
                                    .illegalExtraDTO(IllegalExtraDTO.builder()
                                            .originalFraction(illegalRecordDTO.getDegree())
                                            .originalPenaltyAmount(illegalRecordDTO.getFine())
                                            .build())
                                    .build();
                            vehicleIllegalOrderService.saveIllegalOrderByThird(vehicleIllegalOrderParam, opUserId);
                        }
                    }
                } catch (Exception e) {
                    // !!!入库时系统异常，给个原因到商家
                    VehicleIllegalRecordDetail record = new VehicleIllegalRecordDetail();
                    record.setId(vehicleIllegalRecordDetail.getId());
                    record.setFailReason("信息匹配失败，导致数据无法落库");
                    vehicleIllegalRecordDetailMapper.updateByPrimaryKeySelective(record);
                    throw e;
                }
            }
            return ResultUtil.successResult(VehicleIllegalSearchVO.builder()
                    .result(true)
//                    .expirationDate(mallServiceItemUsageDTO.getItem().getExpirationDate())
                    .RemainingCount(remainingCount)
                    .SuccessCount(successCount)
                    .vehicleCount(vehicleIllegalSearchParamList.size())
                    .build());
        } catch (Exception e) {
            // 运行进度条
            IllegalSearchProgressVO o = (IllegalSearchProgressVO) redisService.get(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId));
            if (o == null) {
                redisService.set(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId),
                        IllegalSearchProgressVO.builder()
                                .progress(0.0)
                                .failReason("系统异常")
                                .build(),
                        5 * 60);
            } else {
                redisService.set(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId),
                        IllegalSearchProgressVO.builder()
                                .progress(o.getProgress())
                                .failReason("系统异常")
                                .build(),
                        5 * 60);
            }
            throw e;
        } finally {
            redisService.remove(redisKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    public Result<Boolean> saveAutoSearchIllegalSetting(VehicleIllegalSettingParam param, Long opUserId) {
        if (param == null || opUserId == null || param.getSearchInterval() == null || param.getMerchantId() == null) {
            return ResultUtil.failResult("参数错误");
        }
        log.info("保存自动查询违章规则,param={},opUserId={}", param, opUserId);
        Long merchantId = param.getMerchantId();
        List<Long> vehicleIdParamList = new ArrayList<>();
        if (param.getVehicleIds() != null) {
            vehicleIdParamList = param.getVehicleIds();
        }
        Set<Long> vehicleIdParamSet = new HashSet<>(vehicleIdParamList);
        vehicleIdParamList = new ArrayList<>(vehicleIdParamSet);
        long time = System.currentTimeMillis();
        VehicleIllegalSettingExample vehicleIllegalSettingExample = new VehicleIllegalSettingExample();
        vehicleIllegalSettingExample.createCriteria().andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleIllegalSetting> vehicleIllegalSettings = vehicleIllegalSettingMapper.selectByExample(vehicleIllegalSettingExample);
        VehicleIllegalSetting vehicleIllegalSetting = new VehicleIllegalSetting();
        vehicleIllegalSetting.setSearchInterval(param.getSearchInterval());
        vehicleIllegalSetting.setOpTime(time);
        vehicleIllegalSetting.setOpUserId(opUserId);
        if (CollectionUtils.isEmpty(vehicleIllegalSettings)) {
            vehicleIllegalSetting.setStatus(YesOrNoEnum.YES.getValue());
            vehicleIllegalSetting.setMerchantId(merchantId);
            vehicleIllegalSetting.setDeleted(YesOrNoEnum.NO.getValue());
            vehicleIllegalSetting.setCreateTime(time);
            vehicleIllegalSettingMapper.insertSelective(vehicleIllegalSetting);
        } else {
            if (vehicleIllegalSettings.size() > 1) {
                return ResultUtil.failResult("自动查询规则数据错误，请联系管理员处理");
            }
            vehicleIllegalSetting.setId(vehicleIllegalSettings.get(0).getId());
            vehicleIllegalSettingMapper.updateByPrimaryKeySelective(vehicleIllegalSetting);
        }

        VehicleIllegalSettingDetailExample vehicleIllegalSettingDetailExample = new VehicleIllegalSettingDetailExample();
        vehicleIllegalSettingDetailExample.createCriteria().andSettingIdEqualTo(vehicleIllegalSetting.getId()).andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleIllegalSettingDetail> originIllegalSettingDetails = vehicleIllegalSettingDetailMapper.selectByExample(vehicleIllegalSettingDetailExample);
        List<Long> changeIds = new ArrayList<>();
        List<Long> changeVehicleIds = new ArrayList<>();
        List<Long> deleteIds = new ArrayList<>();
        for (VehicleIllegalSettingDetail originIllegalSettingDetail : originIllegalSettingDetails) {
            if (vehicleIdParamSet.contains(originIllegalSettingDetail.getVehicleId())) {
                changeIds.add(originIllegalSettingDetail.getId());
                changeVehicleIds.add(originIllegalSettingDetail.getVehicleId());
            } else {
                deleteIds.add(originIllegalSettingDetail.getId());
            }
        }
        vehicleIdParamList.removeAll(changeVehicleIds);
        // 插入新车辆
        List<VehicleIllegalSettingDetail> recordList = new ArrayList<>();
        for (Long vehicleId : vehicleIdParamList) {
            VehicleIllegalSettingDetail record = new VehicleIllegalSettingDetail();
            record.setVehicleId(vehicleId);
            record.setMerchantId(merchantId);
            record.setSettingId(vehicleIllegalSetting.getId());
            record.setDeleted(YesOrNoEnum.NO.getValue());
            record.setCreateTime(time);
            record.setOpTime(time);
            record.setOpUserId(opUserId);
            recordList.add(record);
        }
        if (CollectionUtils.isNotEmpty(recordList)) {
            vehicleIllegalSettingDetailMapper.batchInsert(recordList);
        }
        // 更新不变旧车辆
        if (CollectionUtils.isNotEmpty(changeIds)) {
            VehicleIllegalSettingDetailExample example = new VehicleIllegalSettingDetailExample();
            example.createCriteria().andIdIn(changeIds);
            VehicleIllegalSettingDetail record = new VehicleIllegalSettingDetail();
            record.setOpTime(time);
            record.setOpUserId(opUserId);
            vehicleIllegalSettingDetailMapper.updateByExampleSelective(record, example);
        }

        // 删除变化旧车辆
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            VehicleIllegalSettingDetailExample example = new VehicleIllegalSettingDetailExample();
            example.createCriteria().andIdIn(deleteIds);
            VehicleIllegalSettingDetail record = new VehicleIllegalSettingDetail();
            record.setDeleted(YesOrNoEnum.YES.getValue());
            record.setOpTime(time);
            record.setOpUserId(opUserId);
            vehicleIllegalSettingDetailMapper.updateByExampleSelective(record, example);
        }
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<VehicleIllegalSettingVO> getVehicleIllegalSetting(Long merchantId) {
        if (merchantId == null) {
            return ResultUtil.failResult("参数错误");
        }
        VehicleIllegalSettingExample vehicleIllegalSettingExample = new VehicleIllegalSettingExample();
        vehicleIllegalSettingExample.createCriteria().andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleIllegalSetting> vehicleIllegalSettings = vehicleIllegalSettingMapper.selectByExample(vehicleIllegalSettingExample);
        if (CollectionUtils.isEmpty(vehicleIllegalSettings)) {
            return ResultUtil.successResult(null);
        }
        if (vehicleIllegalSettings.size() > 1) {
            return ResultUtil.failResult("自动查询规则数据错误，请联系管理员处理");
        }
        VehicleIllegalSetting vehicleIllegalSetting = vehicleIllegalSettings.get(0);
        VehicleIllegalSettingDetailExample vehicleIllegalSettingDetailExample = new VehicleIllegalSettingDetailExample();
        VehicleIllegalSettingDetailExample.Criteria criteria = vehicleIllegalSettingDetailExample.createCriteria();
        criteria.andSettingIdEqualTo(vehicleIllegalSetting.getId())
                .andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        VehicleIllegalSettingVO vehicleIllegalSettingVO = new VehicleIllegalSettingVO();
        vehicleIllegalSettingVO.setSearchInterval(vehicleIllegalSetting.getSearchInterval());

        // 违章转移套餐 过滤车辆
//        List<Long> filterVehicleIds = filterTransferVehicle(merchantId);
//        if (filterVehicleIds == null) {
//            return ResultUtil.successResult(vehicleIllegalSettingVO);
//        } else if (!filterVehicleIds.isEmpty()) {
//            criteria.andVehicleIdIn(filterVehicleIds);
//        }

        List<VehicleIllegalSettingDetail> vehicleIllegalSettingDetails = vehicleIllegalSettingDetailMapper.selectByExample(vehicleIllegalSettingDetailExample);

        List<Long> vehicleIds = vehicleIllegalSettingDetails.stream().map(VehicleIllegalSettingDetail::getVehicleId).distinct().collect(Collectors.toList());
        VehicleInfoQueryParam vehicleInfoQueryParam = new VehicleInfoQueryParam();
        vehicleInfoQueryParam.setMerchantId(merchantId);
        vehicleInfoQueryParam.setIdList(vehicleIds);
        Map<Long, VehicleInfoVO> vehicleInfoVOMap =
                vehicleInfoService.listVehicleInfo(vehicleInfoQueryParam).getModel().stream().collect(Collectors.toMap(VehicleInfoVO::getId, e -> e));

        List<VehicleInfoVO> vehicleInfoVOList = new ArrayList<>();
        for (VehicleIllegalSettingDetail vehicleIllegalSettingDetail : vehicleIllegalSettingDetails) {
            Long vehicleId = vehicleIllegalSettingDetail.getVehicleId();
            vehicleInfoVOList.add(vehicleInfoVOMap.get(vehicleId));
        }
        vehicleIllegalSettingVO.setVehicleInfoVOList(vehicleInfoVOList);
        return ResultUtil.successResult(vehicleIllegalSettingVO);
    }

    @Override
    public Result<Boolean> deleteIllegalSettingDetailByVehicleIds(Long merchantId, List<Long> vehicleIds, Long opUserId) {
        if (merchantId == null || vehicleIds == null) {
            return ResultUtil.failResult("参数错误");
        }
        VehicleIllegalSettingDetailExample vehicleIllegalSettingDetailExample = new VehicleIllegalSettingDetailExample();
        vehicleIllegalSettingDetailExample.createCriteria().andVehicleIdIn(vehicleIds)
                .andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        VehicleIllegalSettingDetail record = new VehicleIllegalSettingDetail();
        record.setDeleted(YesOrNoEnum.YES.getValue());
        record.setOpTime(System.currentTimeMillis());
        record.setOpUserId(opUserId);
        vehicleIllegalSettingDetailMapper.updateByExampleSelective(record, vehicleIllegalSettingDetailExample);
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<PageListVo<VehicleIllegalRecordVO>> listBaseIllegalRecord(VehicleIllegalRecordQuery illegalRecordQuery) {
        if (illegalRecordQuery == null || illegalRecordQuery.getMerchantId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        VehicleIllegalRecordExample illegalRecordExample = new VehicleIllegalRecordExample();
        illegalRecordExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andMerchantIdEqualTo(illegalRecordQuery.getMerchantId());
        long totalCount = vehicleIllegalRecordMapper.countByExample(illegalRecordExample);
        if (totalCount <= 0L) {
            return ResultUtil.successResult(PageListVo.buildEmptyPage());
        }
        illegalRecordExample.setOrderByClause(
                String.format("id desc limit %s,%s", illegalRecordQuery.getStartPos(), illegalRecordQuery.getPageSize()));
        List<VehicleIllegalRecord> vehicleIllegalRecords = vehicleIllegalRecordMapper.selectByExample(illegalRecordExample);

        List<Long> mallOrderIds = vehicleIllegalRecords.stream().map(VehicleIllegalRecord::getServiceOrderId).distinct().collect(Collectors.toList());
        MallOrderQuery mallOrderQuery = new MallOrderQuery();
        mallOrderQuery.setOrderIds(mallOrderIds);
        Map<Long, MallServiceOrderInfoDTO> MallServiceOrderInfoMap = mallServiceOrderInfoService.mallOrderList(illegalRecordQuery.getMerchantId(), mallOrderQuery).stream()
                .collect(Collectors.toMap(MallServiceOrderInfoDTO::getId, e -> e));

        List<VehicleIllegalRecordVO> voList =
                vehicleIllegalRecords.stream().map(e -> this.buildBaseIllegalRecordVO(e, MallServiceOrderInfoMap)).collect(Collectors.toList());
        return ResultUtil.successResult(PageListVo.buildPageList(totalCount, voList));
    }

    @Override
    public Result<PageListVo<VehicleIllegalRecordDetailVO>> listIllegalRecord(VehicleIllegalRecordQuery illegalRecordQuery) {
        if (illegalRecordQuery == null || illegalRecordQuery.getMerchantId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        VehicleIllegalRecordDetailExample illegalRecordDetailExample = new VehicleIllegalRecordDetailExample();
        VehicleIllegalRecordDetailExample.Criteria criteria = illegalRecordDetailExample.createCriteria();
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andMerchantIdEqualTo(illegalRecordQuery.getMerchantId());
        long totalCount = vehicleIllegalRecordDetailMapper.countByExample(illegalRecordDetailExample);
        if (totalCount <= 0L) {
            return ResultUtil.successResult(PageListVo.buildEmptyPage());
        }
        if (illegalRecordQuery.getMinId() != null) {
            criteria.andIdLessThan(illegalRecordQuery.getMinId());
        }
        illegalRecordDetailExample.setOrderByClause(
                String.format("id desc limit %s,%s", illegalRecordQuery.getStartPos(), illegalRecordQuery.getPageSize()));
        List<VehicleIllegalRecordDetail> vehicleIllegalRecords = vehicleIllegalRecordDetailMapper.selectByExample(illegalRecordDetailExample);

        List<Long> mallOrderIds = vehicleIllegalRecords.stream().map(VehicleIllegalRecordDetail::getServiceOrderId).distinct().collect(Collectors.toList());
        MallOrderQuery mallOrderQuery = new MallOrderQuery();
        mallOrderQuery.setOrderIds(mallOrderIds);
        Map<Long, MallServiceOrderInfoDTO> MallServiceOrderInfoMap = mallServiceOrderInfoService.mallOrderList(illegalRecordQuery.getMerchantId(), mallOrderQuery).stream()
                .collect(Collectors.toMap(MallServiceOrderInfoDTO::getId, e -> e));
        List<VehicleIllegalRecordDetailVO> voList =
                vehicleIllegalRecords.stream().map(e -> this.buildIllegalRecordVO(e, MallServiceOrderInfoMap)).collect(Collectors.toList());
        return ResultUtil.successResult(PageListVo.buildPageList(totalCount, voList));
    }

    private VehicleIllegalRecordDetailVO buildIllegalRecordVO(VehicleIllegalRecordDetail vehicleIllegalRecordDetail,
                                                              Map<Long, MallServiceOrderInfoDTO> mallServiceOrderInfoMap) {
        VehicleIllegalRecordDetailVO vehicleIllegalRecordVO = new VehicleIllegalRecordDetailVO();
        BeanUtils.copyProperties(vehicleIllegalRecordDetail, vehicleIllegalRecordVO);
        vehicleIllegalRecordVO.setOpTime(vehicleIllegalRecordDetail.getCreateTime());
        if (vehicleIllegalRecordDetail.getOpUserId().equals(-1L)) {
            vehicleIllegalRecordVO.setOpUser("自动查询");
        } else {
            Result<SysUserVo> sysUserVoResult = sysUserService.findById(vehicleIllegalRecordDetail.getOpUserId());
            if (ResultUtil.isModelNotNull(sysUserVoResult)) {
                vehicleIllegalRecordVO.setOpUser(sysUserVoResult.getModel().getName());
            }
        }

        MallServiceOrderInfoDTO mallServiceOrderInfoDTO = mallServiceOrderInfoMap.get(vehicleIllegalRecordDetail.getServiceOrderId());
        if (mallServiceOrderInfoDTO != null) {
            vehicleIllegalRecordVO.setItemName(mallServiceOrderInfoDTO.getItemName());
            vehicleIllegalRecordVO.setItemSubPackage(mallServiceOrderInfoDTO.getItemSubPackage() != null ? mallServiceOrderInfoDTO.getItemSubPackage() : null);
        }
        return vehicleIllegalRecordVO;
    }

    @Override
    public void listIllegalSearchAndSaveByJob() {
        VehicleIllegalSettingExample vehicleIllegalSettingExample = new VehicleIllegalSettingExample();
        vehicleIllegalSettingExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andStatusEqualTo(YesOrNoEnum.YES.getValue());
        List<VehicleIllegalSetting> vehicleIllegalSettings = vehicleIllegalSettingMapper.selectByExample(vehicleIllegalSettingExample);
        for (VehicleIllegalSetting vehicleIllegalSetting : vehicleIllegalSettings) {
            try {
                Long merchantId = vehicleIllegalSetting.getMerchantId();
                Date lastDate = vehicleIllegalSetting.getLastDate();
                LocalDate today = LocalDate.now();
                long timeMillis = System.currentTimeMillis();
                // 按天判断今天是否应该查询
                if (lastDate != null) {
                    LocalDate lastLocalDate = lastDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate targetDate = lastLocalDate.plusDays(vehicleIllegalSetting.getSearchInterval() - 1);
                    if (!targetDate.isBefore(today)) {
                        continue;
                    }
                }
                VehicleIllegalSettingDetailExample vehicleIllegalSettingDetailExample = new VehicleIllegalSettingDetailExample();
                VehicleIllegalSettingDetailExample.Criteria criteria = vehicleIllegalSettingDetailExample.createCriteria();
                criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andMerchantIdEqualTo(merchantId)
                        .andSettingIdEqualTo(vehicleIllegalSetting.getId());

                // 违章转移套餐 过滤车辆
                List<Long> filterVehicleIds = filterTransferVehicle(merchantId);
                if (filterVehicleIds == null) {
                    continue;
                } else if (!filterVehicleIds.isEmpty()) {
                    criteria.andVehicleIdIn(filterVehicleIds);
                }

                List<VehicleIllegalSettingDetail> vehicleIllegalSettingDetails = vehicleIllegalSettingDetailMapper.selectByExample(vehicleIllegalSettingDetailExample);
                List<VehicleIllegalSearchParam> vehicleIllegalSearchParamList = new ArrayList<>();
                vehicleIllegalSettingDetails.forEach(vehicleIllegalSettingDetail -> {
                    VehicleIllegalSearchParam vehicleIllegalSearchParam = new VehicleIllegalSearchParam();
                    vehicleIllegalSearchParam.setVehicleId(vehicleIllegalSettingDetail.getVehicleId());
                    Result<VehicleInfoVO> voResult = vehicleInfoService.getBaseById(vehicleIllegalSettingDetail.getVehicleId(), false);
                    vehicleIllegalSearchParam.setLicenseNo(voResult.getModel().getLicense());
                    vehicleIllegalSearchParamList.add(vehicleIllegalSearchParam);
                });
                Result<VehicleIllegalSearchVO> vehicleIllegalSearchVOResult = vehicleIllegalSearchService.listIllegalSearchAndSave(vehicleIllegalSearchParamList, merchantId, -1L);
                if (ResultUtil.isModelNotNull(vehicleIllegalSearchVOResult)) {
                    VehicleIllegalSetting record = new VehicleIllegalSetting();
                    record.setId(vehicleIllegalSetting.getId());
                    record.setLastDate(new Date(timeMillis));
                    // 没次数或到期 停止自动推送&推送公众号消息
                    if (BooleanUtils.isFalse(vehicleIllegalSearchVOResult.getModel().getResult())) {
                        record.setStatus(YesOrNoEnum.NO.getValue());
                        pushForIllegalNotEnough(merchantId, vehicleIllegalSearchVOResult.getModel().getExpirationDate());
                    }
                    // 开关和上次查询时间仅为内部使用，不改变opTime&opUserId
                    vehicleIllegalSettingMapper.updateByPrimaryKeySelective(record);
                }
            } catch (Exception e) {
                log.error("自动查询异常,merchantId={}", vehicleIllegalSetting.getMerchantId(), e);
            }
        }
    }

    /**
     * 违章转移套餐 过滤车辆  return null 代表过滤所有车辆
     * @param merchantId
     */
    @Override
    public List<Long> filterTransferVehicle(Long merchantId) {
        Result<MallServiceItemUsageDTO> usageDTOResult =
                merchantItemUsage.mallServiceItemUsage(merchantId, MallOrderItemEnum.ILLEGAL_SEARCH.getItemType());
        if (ResultUtil.isModelNull(usageDTOResult)
                || CollectionUtils.isEmpty(usageDTOResult.getModel().getIllTranItem())) {
            return new ArrayList<>();
        }

        List<Long> orderIds = usageDTOResult.getModel().getIllTranItem().stream()
                .filter(e -> e.getExpirationDate().after(new Date()))
                .map(MallServiceOrderInfoDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return null;
        }

        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(merchantId);
        query.setOrderIds(orderIds);
        Result<List<VehicleIllegalTransferDetailVo>> transferDetailList = vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
        if (ResultUtil.isModelNull(transferDetailList)
                || CollectionUtils.isEmpty(transferDetailList.getModel())) {
            return null;
        }
        return transferDetailList.getModel().stream().map(VehicleIllegalTransferDetailVo::getVehicleId).collect(Collectors.toList());

    }

    @Override
    public Result<Boolean> updateIllegalSettingStatus(Long id, Byte status) {
        if (id == null) {
            return ResultUtil.failResult("参数错误");
        }
        VehicleIllegalSetting vehicleIllegalSetting = new VehicleIllegalSetting();
        vehicleIllegalSetting.setId(id);
        vehicleIllegalSetting.setStatus(status);
        // 开关目前仅为内部使用，不改变opTime&opUserId
        vehicleIllegalSettingMapper.updateByPrimaryKeySelective(vehicleIllegalSetting);
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<VehicleIllegalSettingVO> getBaseIllegalSetting(Long merchantId) {
        if (merchantId == null) {
            return ResultUtil.failResult("参数错误");
        }
        VehicleIllegalSetting vehicleIllegalSetting = vehicleIllegalSettingMapper.selectByPrimaryKey(merchantId);
        VehicleIllegalSettingVO vehicleIllegalSettingVO = new VehicleIllegalSettingVO();
        if (vehicleIllegalSetting != null) {
            BeanUtils.copyProperties(vehicleIllegalSetting, vehicleIllegalSettingVO);
            return ResultUtil.successResult(vehicleIllegalSettingVO);
        }
        return ResultUtil.failResult("规则不存在");
    }

    @Override
    public Result<IllegalSearchProgressVO> getProgress(Long merchantId) {
        if (merchantId == null) {
            throw new BizException("参数错误");
        }
        IllegalSearchProgressVO o = (IllegalSearchProgressVO) redisService.get(String.format(RedisConstant.IllegalSearchKey.PROGRESS, merchantId));
        if (o == null) {
            return ResultUtil.failResult("暂无进度");
        }
        return ResultUtil.successResult(o);
    }

    @Override
    public void testPushForIllegalNotEnough(Long merchantId, Date expirationDate) {
        pushForIllegalNotEnough(merchantId, expirationDate);
    }

    @Override
    public Result<VehicleIllegalResponse> getVehicleIllegalBythird(String license, String carType) {
        Result<VehicleIllegalResponse> vehicleIllegal = remoteViolationService.getVehicleIllegal(license, carType);
        log.info("违章查询 bythird,licenseNo={},carType={},vehicleIllegal={}",
                license, carType, JSON.toJSONString(vehicleIllegal));
        return vehicleIllegal;
    }

    /**
     * 获取车辆类型
     * @param licenseNo
     * @return
     */
    private static String getCarType(String licenseNo) {
        String carType;
        if (licenseNo.length() == 7) {
            carType = licenseTypeEnum.SMALL_CAR.getCode();
        } else if (licenseNo.length() == 8) {
            carType = licenseTypeEnum.SMALL_NEW_ENERGY_CAR.getCode();
        } else {
            carType = licenseTypeEnum.SMALL_CAR.getCode();
        }
        return carType;
    }

    /**
     * 自动查询套餐次数不足/到期通知
     */
    private void pushForIllegalNotEnough(Long merchantId, Date expirationDate) {
        // 查询商家 和 商家主账户信息
        SysUserQuery sysUserQuery = SysUserQuery.builder().merchantIdList(Collections.singletonList(merchantId)).
                childAccount(YesOrNoEnum.NO.getValue()).build();
        Result<List<SysUserVo>> sysUserResult = sysUserService.listSysUser(sysUserQuery);
        SysUserVo sysUserVo = sysUserResult.getModel().get(0);
//        Result<MerchantInfoVo> merchantResult = merchantInfoService.findById(merchantId);
//        MerchantInfoVo merchantInfo = merchantResult.getModel();
        PushVO pushVO = new PushVO();
        pushVO.setUserIds(Collections.singletonList(sysUserVo.getId()));
        /*
            公众号订阅通知消息
            mrAB9LTPLD3IqgJFp8-Tpdu7v4T0Pd8Hu2_iKjzdsDg
            权益名称：违章查询服务
            {{thing1.DATA}}
            到期时间：时间
            {{time2.DATA}}
            温馨提示：违章查询次数不足，请尽快续费充值 / 权益即将到期，请在到期前尽快使用
            {{thing3.DATA}}
         */
        Map<String, WxSubscribeMsgVo.Template> data = new HashMap<>();
        data.put("thing1", new WxSubscribeMsgVo.Template("违章查询服务"));
        data.put("time2", new WxSubscribeMsgVo.Template(DateUtil.getFormatDateStr(expirationDate, DateUtil.yyyyMMddHHmm)));
        String remark;
        if (expirationDate.before(new Date())) {
            remark = "违章查询次数不足，请尽快续费充值";
            log.info("自动查询违章停止,违章查询次数不足,商家={}", merchantId);
        } else {
            remark = "权益即将到期，请在到期前尽快使用";
            log.info("自动查询违章停止,违章查询服务到期,商家={}", merchantId);
        }
        data.put("thing3", new WxSubscribeMsgVo.Template(remark));
        pushVO.setMpSubscribeTemplate(WxMpSubscribeTemplateEnum.VEHICLE_ILLEGAL_QUERY_CLOSE.getTemplateId());
        pushVO.setMpSubscribePushObj(data);
        pushMsgService.push(pushVO);
    }

    private VehicleIllegalRecordVO buildBaseIllegalRecordVO(VehicleIllegalRecord vehicleIllegalRecord, Map<Long, MallServiceOrderInfoDTO> mallServiceOrderInfoMap) {
        VehicleIllegalRecordVO vehicleIllegalRecordVO = new VehicleIllegalRecordVO();
        BeanUtils.copyProperties(vehicleIllegalRecord, vehicleIllegalRecordVO);
        vehicleIllegalRecordVO.setOpTime(vehicleIllegalRecord.getCreateTime());
        if (vehicleIllegalRecord.getOpUserId().equals(-1L)) {
            vehicleIllegalRecordVO.setOpUser("自动查询");
        } else {
            Result<SysUserVo> sysUserVoResult = sysUserService.findById(vehicleIllegalRecord.getOpUserId());
            if (ResultUtil.isModelNotNull(sysUserVoResult)) {
                vehicleIllegalRecordVO.setOpUser(sysUserVoResult.getModel().getName());
            }
        }
        MallServiceOrderInfoDTO mallServiceOrderInfoDTO = mallServiceOrderInfoMap.get(vehicleIllegalRecord.getServiceOrderId());
        if (mallServiceOrderInfoDTO != null) {
            vehicleIllegalRecordVO.setItemName(mallServiceOrderInfoDTO != null ? mallServiceOrderInfoDTO.getItemName() : null);
            vehicleIllegalRecordVO.setItemSubPackage(mallServiceOrderInfoDTO.getItemSubPackage() != null ? mallServiceOrderInfoDTO.getItemSubPackage() : null);
        }
        return vehicleIllegalRecordVO;
    }

}
