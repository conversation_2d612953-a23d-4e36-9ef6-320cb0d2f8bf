package com.ql.rent.provider.trade;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.ql.dto.ResultResp;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.RemoteViolationService;
import com.ql.rent.constant.IllegalTransferMessageTemplate;
import com.ql.rent.constant.MallServiceItemConstant;
import com.ql.rent.constant.QyWechatConstant;
import com.ql.rent.dao.trade.MallServiceOrderInfoMapper;
import com.ql.rent.dao.trade.TransferContractStatusMapper;
import com.ql.rent.dao.trade.TransferContractVersionMapper;
import com.ql.rent.dao.trade.VehicleIllegalOrderMapper;
import com.ql.rent.dto.open.AppCertificationDTO;
import com.ql.rent.dto.trade.MallServiceOrderInfoExtraDTO;
import com.ql.rent.dto.trade.TransferReportDTO;
import com.ql.rent.dto.trade.TransferReportSummaryDTO;
import com.ql.rent.dto.trade.TransferReportExcelDTO;
import com.ql.rent.dto.trade.ViolationMonthlyBillExcelDTO;
import com.ql.rent.dto.trade.ViolationTransferSummaryDTO;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.WxMpPageEnum;
import com.ql.rent.enums.WxMpTemplateEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.merchant.PushTypeEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.open.remote.OpenClient;
import com.ql.rent.param.trade.VehicleIllegalOrderInnerQuery;
import com.ql.rent.param.trade.*;
import com.ql.rent.vo.trade.MallServiceOrderInfoDTO;
import com.ql.rent.vo.trade.VehicleIllegalOrderVO;
import com.ql.rent.remote.vehicle.enums.ViolationResultCodeEnum;
import com.ql.rent.remote.vehicle.enums.licenseTypeEnum;
import com.ql.rent.remote.vehicle.vo.request.AbortContractReq;
import com.ql.rent.remote.vehicle.vo.request.CancelContractReq;
import com.ql.rent.remote.vehicle.vo.request.SubmitContractReq;
import com.ql.rent.remote.vehicle.vo.response.SubmitContractResponse;
import com.ql.rent.remote.vehicle.vo.response.SyncContractResponse;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.merchant.TrafficManagementBindService;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.merchant.MerchantInfoService;
import com.ql.rent.service.third.IEnterpriseWechatService;
import com.ql.rent.service.trade.*;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.JsonUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.login.WxMsgVo;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import com.ql.rent.vo.merchant.TrafficManagementBindDTO;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TransferContractStatusServiceImpl implements ITransferContractStatusService {

    private final QingluLogger logger = QingluLoggerFactory.getLogger(TransferContractStatusServiceImpl.class);

    public static final List<String> TRANSFER_AUTH_METHOD = Arrays.asList(
            "qinglu.open.illegal.transfer.contract.transfer",
            "qinglu.open.illegal.transfer.contract.abort",
            "qinglu.open.illegal.transfer.contract.detail",
            "qinglu.open.illegal.transfer.contract.cancel",
            "qinglu.open.illegal.transfer.contract.submit",
            "qinglu.open.saas.violation.query",
            "qinglu.open.illegal.transfer.vehicle.bind",
            "qinglu.open.traffic.management.bind"
    );
    public static final List<String> SEARCH_AUTH_METHOD = List.of(
            "qinglu.open.saas.violation.query"
    );
    @Resource
    private TransferContractStatusMapper transferContractStatusMapper;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderMemberService orderMemberService;
    @Resource
    private TrafficManagementBindService trafficManagementBindService;
    @Resource
    private TransferContractVersionMapper transferContractVersionMapper;
    @Resource
    private VehicleIllegalOrderMapper vehicleIllegalOrderMapper;
    @Resource
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;
    @Resource
    private IMallServiceOrderInfoService mallServiceOrderInfoService;
    @Resource
    private RemoteViolationService remoteViolationService;
    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IOrderSlaveService orderSlaveService;
    @Resource
    private IPushMsgService pushMsgService;
    @Resource
    private IRedisService redisService;

    @Value("${transfer.contract.renewal.days}")
    private Integer renewalDays;

    @Resource
    private OpenClient openClient;
    @Autowired
    private MallServiceOrderInfoMapper mallServiceOrderInfoMapper;

    @Resource
    private IEnterpriseWechatService enterpriseWechatService;
    
    @Resource
    private MerchantInfoService merchantInfoService;
    
    @Resource
    private IPackageSettingService packageSettingService;
    
    @Resource
    private IServiceItemService serviceItemService;
    
    @Resource
    private Executor asyncPromiseExecutor;

    @Override
    public TransferContractStatusVo getTransferStatus(Long merchantId, Long vehicleId, Long orderId, Date illegalTime) {
        if (orderId == null || orderId == 0L) {
            return null;
        }
        TransferContractStatusExample contractStatusExample = new TransferContractStatusExample();
        contractStatusExample.createCriteria().andVehicleIdEqualTo(vehicleId)
                .andOrderIdEqualTo(orderId)
                .andMerchantIdEqualTo(merchantId)
                .andBeginTimeLessThanOrEqualTo(illegalTime)
//                .andStatusNotEqualTo(TransferContractStatusEnum.CANCELLED.getValue())
                .andEndTimeGreaterThanOrEqualTo(illegalTime)
                .andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
        contractStatusExample.setOrderByClause("id desc limit 1");
        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(contractStatusExample);
        if (CollectionUtils.isNotEmpty(contractStatusList)) {
            TransferContractStatus contractStatus = contractStatusList.get(0);
            TransferContractStatusVo contractStatusVo = new TransferContractStatusVo();
            BeanUtils.copyProperties(contractStatus, contractStatusVo);
            return contractStatusVo;
        }
        return null;
    }

    @Override
    public Result<Boolean> saveTransferContractStatus(TransferContractStatusParam param) {
        Long nowTime = System.currentTimeMillis();

        TransferContractStatus transferContractStatus = new TransferContractStatus();
        BeanUtils.copyProperties(param, transferContractStatus);
        transferContractStatus.setOpTime(nowTime);
        if (param.getId() == null) {
            transferContractStatus.setLastVer(1);
            transferContractStatus.setCreateTime(nowTime);
            transferContractStatusMapper.insertSelective(transferContractStatus);
        } else {
            TransferContractStatus oldTransferContract = transferContractStatusMapper.selectByPrimaryKey(param.getId());
            transferContractStatus.setLastVer(oldTransferContract.getLastVer() + 1);
            transferContractStatusMapper.updateByPrimaryKeySelective(transferContractStatus);
        }
        param.setId(transferContractStatus.getId());
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<List<TransferContractStatusVo>> getTransferContractStatus(TransferContractStatusParam param) {
        if (param == null || param.getMerchantId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        TransferContractStatusExample example = new TransferContractStatusExample();
        TransferContractStatusExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(param.getMerchantId());
        if (param.getVehicleId() != null) {
            criteria.andVehicleIdEqualTo(param.getVehicleId());
        }
        if (param.getOrderId() != null) {
            criteria.andOrderIdEqualTo(param.getOrderId());
        }
        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (param.getStartBeginTime() != null) {
            criteria.andBeginTimeGreaterThanOrEqualTo(param.getStartBeginTime());
        }
        if (param.getEndBeginTime() != null) {
            criteria.andBeginTimeLessThanOrEqualTo(param.getEndBeginTime());
        }
        if (param.getStartEndTime() != null) {
            criteria.andEndTimeGreaterThanOrEqualTo(param.getStartEndTime());
        }
        if (param.getEndEndTime() != null) {
            criteria.andEndTimeLessThanOrEqualTo(param.getEndEndTime());
        }
        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            criteria.andStatusIn(param.getStatusList());
        }
        if (param.getBizType() != null) {
            criteria.andBizTypeEqualTo(param.getBizType());
        }
        if (param.getContractNo() != null) {
            criteria.andContractNoEqualTo(param.getContractNo());
        }

        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(example);
        List<TransferContractStatusVo> contractStatusVoList = new ArrayList<>();
        for (TransferContractStatus transferContractStatus : contractStatusList) {
            TransferContractStatusVo transferContractStatusVo = new TransferContractStatusVo();
            BeanUtils.copyProperties(transferContractStatus, transferContractStatusVo);
            contractStatusVoList.add(transferContractStatusVo);
        }

        return ResultUtil.successResult(contractStatusVoList);
    }

    @Override
    public TransferContractStatusVo getLatestContract(Long merchantId, Long vehicleId, Long orderId) {
        // 拉取更新最新合同
//        this.syncContractByJob();

        TransferContractStatusExample example = new TransferContractStatusExample();
        TransferContractStatusExample.Criteria criteria = example.createCriteria().andMerchantIdEqualTo(merchantId);
//        List<Byte> statusList = TransferContractStatusEnum.needAbort();
//        statusList.add(TransferContractStatusEnum.ABORTED.getValue());
        criteria.andStatusIn(TransferContractStatusEnum.maySuccessReport())
                .andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
        if (vehicleId != null) {
            criteria.andVehicleIdEqualTo(vehicleId);
        }
        if (orderId != null) {
            criteria.andOrderIdEqualTo(orderId);
        }
        // 三方更新接口可能获取不到最新状态，加上id排序处理下
        example.setOrderByClause("end_time desc,id desc limit 1");
        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(contractStatusList)) {
            return null;
        }
        TransferContractStatus contractStatus = contractStatusList.get(0);
        TransferContractStatusVo vo = new TransferContractStatusVo();
        BeanUtils.copyProperties(contractStatus, vo);
        return vo;
    }

    private Date truncateToMinutes(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.SECOND, 0);      // 清除秒
        calendar.set(Calendar.MILLISECOND, 0); // 清除毫秒
        return calendar.getTime();
    }

    @Override
    public Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime) {
        return ResultUtil.successResult(submitContract(orderId, beginTime, endTime, null));
    }

    @Override
    public Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime, Long vehicleId) {
        if (orderId == null || beginTime == null || endTime == null) {
            return ResultUtil.failResult("参数错误");
        }

        Calendar instance = Calendar.getInstance();
        Date tempEndTime = beginTime;
        instance.setTime(tempEndTime);
        instance.add(Calendar.MINUTE, -1);
        tempEndTime = instance.getTime();

        // 合同上报时间段不可超过一个月
        while (tempEndTime.compareTo(endTime) < 0) {
            instance.setTime(tempEndTime);
            instance.add(Calendar.MINUTE, 1);
            Date tempBeginTime = instance.getTime();

            instance.add(Calendar.DAY_OF_MONTH, renewalDays);
            tempEndTime = instance.getTime();
            if (tempEndTime.after(endTime)) {
                tempEndTime = endTime;
            }

            Result<Boolean> ret = this.submitOneContract(orderId, tempBeginTime, tempEndTime, vehicleId);
            if (!ret.isSuccess()) {
                return ResultUtil.failResult(ret.getMessage());
            }
        }
        return ResultUtil.successResult(true);
    }

    /**
     * 上传1份合同
     */
    private Result<Boolean> submitOneContract(Long orderId, Date beginTime, Date endTime, Long vehicleId) {

        Result<OrderInfoVo> orderInfoResult = orderService.getOrderInfo(orderId);
        if (ResultUtil.isModelNull(orderInfoResult)) {
            logger.startLog().with("orderId", orderId).with("beginTime", beginTime).with("endTime", endTime)
                    .logAudit("违章转移 订单不存在");
            return ResultUtil.failResult("订单不存在");
        }
        OrderInfoVo orderInfo = orderInfoResult.getModel();
        Long merchantId = orderInfo.getMerchantId();
        String license;
        if (vehicleId != null) {
            Result<VehicleInfoVO> vehicleInfoVOResult = vehicleInfoService.getBaseById(vehicleId, false);
            if (ResultUtil.isModelNull(vehicleInfoVOResult)) {
                logger.startLog().with("orderId", orderId).with("beginTime", beginTime).with("endTime", endTime)
                        .logAudit("违章转移 车辆不存在");
                return ResultUtil.failResult("车辆不存在");
            }
            license = vehicleInfoVOResult.getModel().getLicense();
        } else {
            vehicleId = orderInfo.getVehicleId();
            license = orderInfo.getVehicleNo();
        }

        // 判断是否有可用内部转移订单
        MallServiceOrderParam param = new MallServiceOrderParam();
        param.setOrderStatus(MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue());
        param.setStartExpirationDate(new Date());
        param.setItemType(MallOrderItemEnum.ILLEGAL_SEARCH.getItemType());
        List<MallServiceOrderInfoDTO> serviceOrderInfoDTOS =
                mallServiceOrderInfoService.serviceOrderList(merchantId, param);
        if (CollectionUtils.isEmpty(serviceOrderInfoDTOS)) {
            return ResultUtil.failResult("无可用内部转移订单");
        }
        List<MallServiceOrderInfoDTO> availableOrderList = serviceOrderInfoDTOS.stream()
                .filter(e -> MallServiceItemConstant.ILLEGAL.TRANSFER.equals(e.getItemSubPackage())
                        && (e.getMallServiceOrderInfoExtraDTO() == null || e.getMallServiceOrderInfoExtraDTO().getIsOpen() == null
                        || Boolean.FALSE.equals(e.getMallServiceOrderInfoExtraDTO().getIsOpen())))
                .collect(Collectors.toList());
        if (availableOrderList.isEmpty()) {
            return ResultUtil.failResult("无可用内部转移订单");
        }

        // 查找重叠的合同
        TransferContractStatusParam contractStatusParam = new TransferContractStatusParam();
        contractStatusParam.setMerchantId(merchantId);
        contractStatusParam.setVehicleId(vehicleId);
        contractStatusParam.setStartEndTime(beginTime);
        contractStatusParam.setEndBeginTime(endTime);
        contractStatusParam.setBizType(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
        Result<List<TransferContractStatusVo>> transferContractStatus = this.getTransferContractStatus(contractStatusParam);
        TransferContractStatusVo sameTransferContract = null;
        if (CollectionUtils.isNotEmpty(transferContractStatus.getModel())) {
            // 查找相同合同
            Long finalVehicleId = vehicleId;
            sameTransferContract = transferContractStatus.getModel().stream()
                    .filter(e -> e.getOrderId().equals(orderId)
                            && e.getVehicleId().equals(finalVehicleId)
                            && e.getBeginTime().equals(beginTime)
                            && e.getEndTime().equals(endTime)
                            && e.getMerchantId().equals(merchantId))
                    .findFirst().orElse(null);
        }

        TransferContractStatusParam saveParam = TransferContractStatusParam.builder()
                .id(sameTransferContract != null ? sameTransferContract.getId() : null)
                .orderId(orderId)
                .vehicleId(vehicleId)
                .carNumber(license)
                .beginTime(beginTime)
                .endTime(endTime)
                .bizType(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode())
                .merchantId(merchantId)
                .carType(getCarType(license))
                .build();

        if (sameTransferContract == null) {
            // 预保存记录
            saveParam.setContractNo(generateContractNo(orderId));
            this.saveTransferContractStatus(saveParam);
        } else {
            saveParam.setContractNo(sameTransferContract.getContractNo());
            // 重复上报的直接return
            if (TransferContractStatusEnum.repeatReport().contains(sameTransferContract.getStatus())) {
                return ResultUtil.successResult(true);
            }
        }

        // 内部业务check
        String errorMessage = checkSubmitContract(beginTime, endTime, merchantId, orderInfo, transferContractStatus.getModel(), availableOrderList, saveParam);

        if (StringUtils.isNotBlank(errorMessage)) {
            saveParam.setErrorMsg(errorMessage);
            saveParam.setStatus(TransferContractStatusEnum.REPORT_FAIL.getValue());
            // 更新合同
            this.saveTransferContractStatus(saveParam);
            // 更新违章的转移状态
            updIllegalTransferStatus(saveParam.getVehicleId(), saveParam.getOrderId(), saveParam.getBeginTime(),
                    saveParam.getEndTime(), (int) TransferContractStatusEnum.REPORT_FAIL.getValue());
            // 发送企业微信通知
            String content = IllegalTransferMessageTemplate.formatSaasMerchantReportFail(
                    merchantId, orderId, orderInfo.getVehicleNo(), saveParam.getContractNo(), null, errorMessage);
            enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, merchantId, null);
            return ResultUtil.failResult(errorMessage);
        }

        // 构建上报参数
        SubmitContractReq submitContractReq = new SubmitContractReq();
        submitContractReq.setRentType(2);
        submitContractReq.setCarType(getCarType(orderInfo.getVehicleNo()));
        submitContractReq.setAccount(saveParam.getAccount());
        submitContractReq.setDriverName(saveParam.getDriverName());
        submitContractReq.setDriverPhone(saveParam.getDriverPhone());
        submitContractReq.setDriverId(saveParam.getDriverId());
        submitContractReq.setContractNo(saveParam.getContractNo());
        submitContractReq.setCarNumber(orderInfo.getVehicleNo());
        submitContractReq.setBeginTime(beginTime);
        submitContractReq.setEndTime(endTime);

        // 调用三方录入合同
        Result<SubmitContractResponse> responseResult = remoteViolationService.submitContract(submitContractReq);
        Integer state;
        if (ResultUtil.isModelNotNull(responseResult)) {
            SubmitContractResponse contractResponse = responseResult.getModel();
            state = contractResponse.getState();
            saveParam.setContractId(contractResponse.getOrderId());
            saveParam.setStatus(contractResponse.getState().byteValue());
            saveParam.setMessage(contractResponse.getPostMsg());
            saveParam.setPostCode(contractResponse.getPostCode());
            this.pushTransferMsg(orderInfo, contractResponse, beginTime, endTime, submitContractReq.getAccount());

            // 合同状态为0上报中时，如果postCode失败,发送企业微信通知
            if (SubmitContractResponse.isPostFail(contractResponse)
                    && Integer.valueOf(TransferContractStatusEnum.REPORTING.getValue()).equals(state)) {
                String content = IllegalTransferMessageTemplate.formatSaasMerchantReporting(
                        merchantId, orderId, orderInfo.getVehicleNo(), submitContractReq.getContractNo(), contractResponse.getOrderId(), contractResponse.getPostMsg());
                enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, merchantId, null);
            }
        } else {
            state = (int) TransferContractStatusEnum.REPORT_FAIL.getValue();
            saveParam.setErrorMsg(responseResult.getMessage());
            saveParam.setStatus(state.byteValue());
            // 发送企业微信通知
            String content = IllegalTransferMessageTemplate.formatSaasMerchantReportFail(
                    merchantId, orderId, orderInfo.getVehicleNo(), submitContractReq.getContractNo(), null, responseResult.getMessage());
            enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, merchantId, null);
        }
        this.saveTransferContractStatus(saveParam);

        // 更新违章的转移状态
        updIllegalTransferStatus(saveParam.getVehicleId(), saveParam.getOrderId(), submitContractReq.getBeginTime(),
                submitContractReq.getEndTime(), state);

        return ResultUtil.successResult(true);
    }

    @NotNull
    private String getContractNo(Long orderId) {
        return generateContractNo(orderId);
    }

    private String checkSubmitContract(Date beginTime, Date endTime, Long merchantId, OrderInfoVo orderInfo, List<TransferContractStatusVo> transferContractStatus, List<MallServiceOrderInfoDTO> availableOrderList, TransferContractStatusParam saveParam) {
        Long orderId = orderInfo.getId();

        // 查询该车辆是否允许被转移
        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(merchantId);
        query.setVehicleId(orderInfo.getVehicleId());
        query.setOrderIds(new ArrayList<>(availableOrderList.stream().map(MallServiceOrderInfoDTO::getId).collect(Collectors.toSet())));
        Result<List<VehicleIllegalTransferDetailVo>> transferDetailListResult =
                vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
        if (ResultUtil.isModelNull(transferDetailListResult) || CollectionUtils.isEmpty(transferDetailListResult.getModel())) {
            logger.startLog().with("orderId", orderId).with("beginTime", beginTime).with("endTime", endTime)
                    .logAudit("违章转移 车辆未绑定转移订单");
            return "车辆未绑定转移订单";
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        // 产品需求 内部不校验合同开始时间需要在近24小时内
//        if (beginTime.before(calendar.getTime())) {
//            logger.startLog().with("orderId", orderId).with("beginTime", beginTime).with("endTime", endTime)
//                    .logAudit("违章转移 参数check 合同开始时间需要在近24小时内");
//            return "合同开始时间需要在近24小时内";
//        }
        if (truncateToMinutes(beginTime).compareTo(truncateToMinutes(endTime)) >= 0) {
            logger.startLog().with("orderId", orderId).with("beginTime", beginTime).with("endTime", endTime)
                    .logAudit("违章转移 参数check 合同结束时间必须晚于开始时间一分钟");
            return "合同结束时间必须晚于开始时间一分钟";
        }

        Result<UserCertificateVo> userCertificateVoResult = orderMemberService.getUserCertificate(orderId);
        if (ResultUtil.isModelNull(userCertificateVoResult)) {
            return "用户信息错误";
        }
        UserCertificateVo userCertificateVo = userCertificateVoResult.getModel();
        saveParam.setDriverName(userCertificateVo.getUserName());
        saveParam.setDriverId(userCertificateVo.getIdcardNo());
        saveParam.setDriverPhone(userCertificateVo.getMobile());
        if (!IdcardUtil.isValidCard(userCertificateVo.getIdcardNo())) {
            logger.startLog().with("orderId", orderId).with("idcardNo", userCertificateVo.getIdcardNo())
                    .logAudit("违章转移 录入check 身份证信息错误");
            return "身份证信息错误：" + userCertificateVo.getIdcardNo();
        }

        // 根据车辆id&merchantid 查 122 账号
        TrafficManagementBindDTO trafficManagementBindDTO =
                trafficManagementBindService.selectIllegalTransferAccount(merchantId, orderInfo.getVehicleId());
        if (trafficManagementBindDTO == null) {
            logger.startLog().with("orderId", orderId).with("vehicleId", orderInfo.getVehicleId())
                    .logAudit("违章转移 录入check 该车辆未绑定122账号");
            return "该车辆未绑定122账号";
        }
        saveParam.setAccount(trafficManagementBindDTO.getUsername());


        // 产品需求 内部不校验重复
//        List<TransferContractStatusVo> checkList = transferContractStatus.stream()
//                .filter(e -> TransferContractStatusEnum.repeatReport().contains(e.getStatus())).collect(Collectors.toList());
//        if(CollectionUtils.isNotEmpty(checkList)) {
//            logger.startLog().with("orderId", orderId).with("beginTime", beginTime).with("endTime", endTime).with("transferContractStatus", transferContractStatus)
//                    .logAudit("违章转移 录入check 合同时间段重叠");
//            return "合同时间段重叠:" + transferContractStatus.get(0).getContractId() + ",订单id:" + transferContractStatus.get(0).getOrderId();
//        }
        return "";
    }

    /**
     * 重试相同合同
     */
    public void retrySameContract(TransferContractStatus param, boolean isCheck) {
        Date beginTime = param.getBeginTime();
        Date endTime = param.getEndTime();

        if (isCheck) {
            // 合同开始时间需要在近24小时内
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            if (beginTime.before(calendar.getTime())) {
                logger.startLog().with("param", param).logAudit("违章转移 重试合同 参数check 合同开始时间需要在近24小时内");
                return;
            }

            if (!IdcardUtil.isValidCard(param.getDriverId())) {
                logger.startLog().with("param", param).logAudit("违章转移 重试合同 参数check 合同身份证错误");
                return;
            }

            if (truncateToMinutes(beginTime).compareTo(truncateToMinutes(endTime)) >= 0) {
                logger.startLog().with("param", param).logAudit("违章转移 重试合同 参数check 合同结束时间必须晚于开始时间一分钟");
                return;
            }

            // 判断同一辆车的合同时间段不重叠
            TransferContractStatusParam contractStatusParam = new TransferContractStatusParam();
            contractStatusParam.setMerchantId(param.getMerchantId());
            contractStatusParam.setVehicleId(param.getVehicleId());
            contractStatusParam.setStartEndTime(beginTime);
            contractStatusParam.setEndBeginTime(endTime);
            contractStatusParam.setStatusList(TransferContractStatusEnum.repeatReport());
            contractStatusParam.setBizType(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
            Result<List<TransferContractStatusVo>> transferContractStatusList = this.getTransferContractStatus(contractStatusParam);
            if (transferContractStatusList.isSuccess() && CollectionUtils.isNotEmpty(transferContractStatusList.getModel())) {
                logger.startLog().with("param", param).with("beginTime", beginTime).with("endTime", endTime)
                        .with("contractId", transferContractStatusList.getModel().get(0).getContractId())
                        .with("param", param)
                        .logAudit("违章转移 重试合同 录入check 合同时间段重叠");
                return;
            }
        }

        SubmitContractReq submitContractReq = new SubmitContractReq();
        submitContractReq.setAccount(param.getAccount());
        submitContractReq.setDriverName(param.getDriverName());
        submitContractReq.setDriverPhone(param.getDriverPhone());
        submitContractReq.setDriverId(param.getDriverId());
        submitContractReq.setCarNumber(param.getCarNumber());
        submitContractReq.setCarType(param.getCarType());
        submitContractReq.setContractNo(param.getContractNo());
        submitContractReq.setBeginTime(param.getBeginTime());
        submitContractReq.setEndTime(endTime);
        submitContractReq.setContractNo(param.getContractNo());
        submitContractReq.setRentType(2);

        // 调用三方重试合同
        logger.startLog().with("orderId", param.getOrderId()).with("isCheck", isCheck).with("param", param).logAudit("违章转移 重试合同");
        Result<SubmitContractResponse> responseResult = remoteViolationService.submitContract(submitContractReq);
        Integer state;
        TransferContractStatusParam updParam = new TransferContractStatusParam();
        updParam.setId(param.getId());
        if (ResultUtil.isModelNotNull(responseResult)) {
            SubmitContractResponse contractResponse = responseResult.getModel();
            state = contractResponse.getState();
            updParam.setContractId(contractResponse.getOrderId());
            updParam.setStatus(contractResponse.getState().byteValue());
            updParam.setMessage(contractResponse.getPostMsg());
            updParam.setPostCode(contractResponse.getPostCode());
        } else {
            state = (int) TransferContractStatusEnum.REPORT_FAIL.getValue();
            updParam.setErrorMsg(responseResult.getMessage());
            updParam.setStatus(state.byteValue());
        }
        this.saveTransferContractStatus(updParam);

        // 更新违章的转移状态
        updIllegalTransferStatus(param.getVehicleId(), param.getOrderId(), submitContractReq.getBeginTime(),
                submitContractReq.getEndTime(), state);

    }

    @Override
    public void testPushTransferMsg(Long orderId, String code, Date beginTime, Date endTime, String account) {
        Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(orderId);
        if (ResultUtil.isModelNull(orderInfoVoResult)) {
            throw new BizException("订单不存在");
        }
        SubmitContractResponse contractResponse = new SubmitContractResponse();
        contractResponse.setPostCode(code);
        this.pushTransferMsg(orderInfoVoResult.getModel(), contractResponse, beginTime, endTime, account);
    }

    @Override
    public Result<Boolean> authorizeIllegal(Long orderId, LoginVo loginVo) {
        if (loginVo == null || orderId == null) {
            return ResultUtil.failResult("参数错误");
        }

        log.info("开始违章转移授权, orderId: {}, userId: {}", orderId, loginVo.getUserId());

        // 根据订单ID查询违章转移订单
        MallServiceOrderInfoDTO serviceOrderInfoDTO =
                mallServiceOrderInfoService.detail(loginVo.getMerchantId(), orderId, null);
        if (serviceOrderInfoDTO == null) {
            return ResultUtil.failResult("违章转移订单不存在");
        }

        // 从DTO中获取merchantId，需要先查看MallServiceOrderInfoDTO的结构
        Long merchantId = serviceOrderInfoDTO.getMerchantId();

        // 调用open应用查询app_certification反查到应用信息
        ResultResp<AppCertificationDTO> appResult = openClient.queryAppCertificationByMerchantId(merchantId);
        if (!ResultResp.isSuccess(appResult) || appResult.getData() == null) {
            return ResultUtil.failResult("未查询到商家应用认证信息");
        }

        // 需要授权的违章转移接口
        List<String> interfaceCodeList;
        if (MallServiceItemConstant.ILLEGAL.TRANSFER.equals(serviceOrderInfoDTO.getItemSubPackage())) {
            interfaceCodeList = TRANSFER_AUTH_METHOD;
        } else if (MallServiceItemConstant.ILLEGAL.SERVICE.equals(serviceOrderInfoDTO.getItemSubPackage())) {
            interfaceCodeList = SEARCH_AUTH_METHOD;
        } else {
            return ResultUtil.failResult("不支持的订单类型");
        }

        // 调用open应用的授权接口
        ResultResp<Boolean> authorizeResult = openClient.authorize(
                appResult.getData().getAppId().toString(),
                merchantId.toString(),
                "1", // 启用
                "1", // 启用
                "0", // 未删除
                "15", // 访问次数
                interfaceCodeList
        );

        if (!ResultResp.isSuccess(authorizeResult)) {
            log.error("违章转移接口授权失败, orderId: {}, merchantId: {}", orderId, merchantId);
            return ResultUtil.failResult("授权失败：" + authorizeResult.getMessage());
        }

        // 将违章转移订单打标
        MallServiceOrderInfoExtraDTO extraDTO = serviceOrderInfoDTO.getMallServiceOrderInfoExtraDTO();
        if (extraDTO == null) {
            extraDTO = new MallServiceOrderInfoExtraDTO();
        }
        extraDTO.setIsOpen(true);
        try {
            MallServiceOrderInfo orderInfo = new MallServiceOrderInfo();
            orderInfo.setId(orderId);
            orderInfo.setExtra(JsonUtil.beanToJson(extraDTO));
            orderInfo.setOpTime(System.currentTimeMillis());
            orderInfo.setOpUserId(loginVo.getUserId());

            mallServiceOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);
        } catch (Exception e) {
            log.error("更新订单扩展信息失败, orderId: {}, merchantId: {}", orderId, merchantId, e);
            return ResultUtil.failResult("更新失败");
        }
        log.info("违章转移授权成功, orderId: {}, merchantId: {}", orderId, merchantId);

        return ResultUtil.successResult(true);
    }

    @Override
    public Result<QueryIllegalTransferAuthVO> queryIllegalTransferAuth(Long orderId, Long merchantId) {
        if (orderId == null || merchantId == null) {
            return ResultUtil.failResult("参数错误");
        }
        QueryIllegalTransferAuthVO queryIllegalTransferAuthVO = new QueryIllegalTransferAuthVO();

        // 查询违章转移订单
        MallServiceOrderInfoDTO serviceOrderInfoDTO = mallServiceOrderInfoService.detail(merchantId, orderId, null);
        if (serviceOrderInfoDTO == null) {
            return ResultUtil.failResult("违章转移订单不存在");
        }

        // 检查是否打标
        MallServiceOrderInfoExtraDTO extraDTO = serviceOrderInfoDTO.getMallServiceOrderInfoExtraDTO();
        if (extraDTO == null || !Boolean.TRUE.equals(extraDTO.getIsOpen())) {
            queryIllegalTransferAuthVO.setAuthorized(false);
            return ResultUtil.successResult(queryIllegalTransferAuthVO);
        }


        // 查询open接口，获取appKey和appSecret
        ResultResp<AppCertificationDTO> appResult = openClient.queryAppCertificationByMerchantId(merchantId);
        if (!ResultResp.isSuccess(appResult) || appResult.getData() == null) {
            log.warn("查询商家应用认证信息失败, orderId: {}, merchantId: {}", orderId, merchantId);
            return ResultUtil.failResult("查询商家应用认证信息失败");
        }
        AppCertificationDTO appCertificationDTO = appResult.getData();

        // 组装返回数据：appKey、appSecret、merchantId
        queryIllegalTransferAuthVO.setAppKey(appCertificationDTO.getAppKey());
        queryIllegalTransferAuthVO.setAppSecret(appCertificationDTO.getAppSecret());
        queryIllegalTransferAuthVO.setMerchantId(merchantId);
        queryIllegalTransferAuthVO.setAuthorized(true);
        return ResultUtil.successResult(queryIllegalTransferAuthVO);
    }

    /**
     * if (是有效违章转移订单的车辆 && 订单状态在已排车 && 订单的身份证错误)
     *   异步发送订单身份证不正确企业微信消息
     */
    @Override
    public void sendOrderIdCardErrorMsg(Long orderId) {
        if (orderId == null) {
            return;
        }
        
        CompletableFuture.runAsync(() -> {
            try {
                // 1. 查询订单信息
                Result<OrderInfoVo> orderInfoResult = orderService.getOrderInfo(orderId);
                if (ResultUtil.isModelNull(orderInfoResult)) {
                    return;
                }
                
                OrderInfoVo orderInfo = orderInfoResult.getModel();
                Long merchantId = orderInfo.getMerchantId();
                Long vehicleId = orderInfo.getVehicleId();
                String vehicleNo = orderInfo.getVehicleNo();
                
                // 2. 检查订单状态是否为已排车
                if (!OrderStatusEnum.isScheduled(orderInfo.getOrderStatus())) {
                    return;
                }
                
                // 3. 检查车辆是否绑定有效的违章转移订单
                TransferVehicleBindQuery query = new TransferVehicleBindQuery();
                query.setMerchantId(merchantId);
                query.setVehicleId(vehicleId);
                
                Result<List<VehicleIllegalTransferDetailVo>> transferDetailListResult =
                        vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
                        
                if (ResultUtil.isModelNull(transferDetailListResult) || CollectionUtils.isEmpty(transferDetailListResult.getModel())) {
                    return;
                }
                
                // 4. 检查违章转移订单是否有效（已支付且未过期）
                List<Long> transferOrderIds = transferDetailListResult.getModel().stream()
                        .map(VehicleIllegalTransferDetailVo::getOrderId)
                        .distinct()
                        .collect(Collectors.toList());
                        
                MallServiceOrderParam param = new MallServiceOrderParam();
                param.setOrderIds(transferOrderIds);
                param.setOrderStatus(MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue());
                param.setStartExpirationDate(new Date());
                param.setItemType(MallOrderItemEnum.ILLEGAL_SEARCH.getItemType());
                
                List<MallServiceOrderInfoDTO> validTransferOrders = mallServiceOrderInfoService.serviceOrderList(merchantId, param);
                if (CollectionUtils.isEmpty(validTransferOrders)) {
                    return;
                }
                
                // 过滤出违章转移类型 并且 没有开放open接口的订单
                List<MallServiceOrderInfoDTO> transferTypeOrders = validTransferOrders.stream()
                        .filter(e -> MallServiceItemConstant.ILLEGAL.TRANSFER.equals(e.getItemSubPackage()) &&
                                (e.getMallServiceOrderInfoExtraDTO() == null || e.getMallServiceOrderInfoExtraDTO().getIsOpen() == null
                                        || !e.getMallServiceOrderInfoExtraDTO().getIsOpen()))
                        .collect(Collectors.toList());
                        
                if (CollectionUtils.isEmpty(transferTypeOrders)) {
                    return;
                }
                
                // 5. 获取用户身份证信息并验证
                Result<UserCertificateVo> userCertificateResult = orderMemberService.getUserCertificate(orderId);
                if (ResultUtil.isModelNull(userCertificateResult)) {
                    return;
                }
                
                UserCertificateVo userCertificate = userCertificateResult.getModel();
                String idcardNo = userCertificate.getIdcardNo();
                
                // 6. 验证身份证是否有效
                if (StringUtils.isNotBlank(idcardNo) && IdcardUtil.isValidCard(idcardNo)) {
                    return;
                }
                
                // 7. 身份证有误，发送企业微信消息
                String message = IllegalTransferMessageTemplate.formatMerchantIdCardError(merchantId, orderId, vehicleNo);
                
                enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, message, merchantId, null);
                
                logger.startLog().with("orderId", orderId).with("merchantId", merchantId)
                        .with("vehicleNo", vehicleNo).with("idcardNo", idcardNo)
                        .log("发送身份证错误企业微信消息");
                        
            } catch (Exception e) {
                logger.startLog().with("orderId", orderId).logError("检查身份证错误异常", e);
            }
        }, asyncPromiseExecutor);
    }

    private void pushTransferMsg(OrderInfoVo orderInfo, SubmitContractResponse contractResponse, Date beginTime, Date endTime, String account) {
        try {
            ViolationResultCodeEnum.TransferContractEnum responseEnum = ViolationResultCodeEnum.TransferContractEnum.getByCode(contractResponse.getPostCode());
            if (responseEnum != null && responseEnum.getIsPushMessage()) {
                String rdKey = "pushTransferMsg:" + orderInfo.getId() + "_" + orderInfo.getVehicleId();
                // 24小时内 同一订单+车辆的上报不发消息
                long check = redisService.setnx(rdKey, 1, TimeUnit.DAYS);
                if (check > 1) {
                    logger.startLog().with("orderId", orderInfo.getId()).with("vehicleNo", orderInfo.getVehicleNo())
                            .with("beginTime", beginTime).with("endTime", endTime)
                            .with("contractResponse", contractResponse)
                            .log("违章转移 消息推送, 24小时内同一订单+车辆的上报错误不发消息");
                    return;
                }
                // 公众号通知上报失败
                /*
                 * 违章错误码对接，当违章接口收到错误码时，通过微信公众号发送消息
                 * 错误码参考附件，触发条件：当不符合上报条件时触发消息/接口中获取到错误码消息触发消息
                 * 模板ID：bERQiHT1GEPzWXPhWSM7NaNR5WrfMxQWyVllClmZewQ
                 *
                 * 企业账号{{character_string1.DATA}}
                 * 租车订单号{{character_string2.DATA}}
                 * 车牌号{{car_number3.DATA}}
                 * 失败原因{{const4.DATA}} 管理枚举值
                 */
                PushVO pushVO = new PushVO();
                Map<String, WxMsgVo.Template> data = new HashMap<>();
                data.put("character_string1", new WxMsgVo.Template(account));
                data.put("character_string2", new WxMsgVo.Template(orderInfo.getId().toString()));
                data.put("car_number3", new WxMsgVo.Template(orderInfo.getVehicleNo()));
                data.put("const4", new WxMsgVo.Template(responseEnum.getSolution()));
                pushVO.setMpTemplate(WxMpTemplateEnum.ILLEGAL_REPORT_FAILURE.getTemplateId());
                pushVO.setMpPushObj(data);

                // ------通知对象参数--------
                pushVO.setMerchantId(orderInfo.getMerchantId());
                pushVO.setStoreId(orderInfo.getPickupStoreId());
                pushVO.setPushTypeEnum(PushTypeEnum.TYPE_ILLEGAL);
                if (ViolationResultCodeEnum.TransferContractEnum.isNeedLogin(contractResponse.getPostCode())) {
                    pushVO.setPagePath(WxMpPageEnum.VIOLATION_LOGIN.getPage() + account);
                }

                logger.startLog().with("orderId", orderInfo.getId()).with("beginTime", beginTime).with("endTime", endTime)
                        .with("contractResponse", contractResponse).log("违章转移 消息推送");
                pushMsgService.push(pushVO);
            }
        } catch (Exception e) {
            logger.startLog().with("orderId", orderInfo.getId()).with("beginTime", beginTime).with("endTime", endTime)
                    .with("contractResponse", contractResponse).logError("违章转移 消息推送异常", e);
        }
    }

    private void updIllegalTransferStatus(Long vehicleId, Long orderId, Date illegalStartTime, Date illegalEndTime, Integer state) {
        List<VehicleIllegalOrderVO> illegalOrderList = vehicleIllegalOrderService.getBaseVehicleIllegalOrder(VehicleIllegalOrderInnerQuery.builder()
                .vehicleId(vehicleId)
                .orderId(orderId)
                .illegalStartTime(illegalStartTime)
                .illegalEndTime(illegalEndTime)
                .build());
        for (VehicleIllegalOrderVO illegalOrderVO : illegalOrderList) {
            VehicleIllegalOrder updIllegalOrder = new VehicleIllegalOrder();
            updIllegalOrder.setId(illegalOrderVO.getId());
            updIllegalOrder.setTransferStatus(state.byteValue());
            updIllegalOrder.setLastVer(illegalOrderVO.getLastVer() + 1);
            vehicleIllegalOrderMapper.updateByPrimaryKeySelective(updIllegalOrder);
        }
    }

    /**
     * 获取车辆类型
     * @param licenseNo
     * @return
     */
    private static String getCarType(String licenseNo) {
        String carType;
        if (licenseNo.length() == 7) {
            carType = licenseTypeEnum.SMALL_CAR.getCode();
        } else if (licenseNo.length() == 8) {
            carType = licenseTypeEnum.SMALL_NEW_ENERGY_CAR.getCode();
        } else {
            carType = licenseTypeEnum.SMALL_CAR.getCode();
        }
        return carType;
    }

    private String generateContractNo(Long orderId) {
        return UuidUtil.getUUID();
    }

    @Override
    public Boolean abortAllContract(Long orderId, Long vehicleId, Date abortDate, boolean needSubmit) {
        List<TransferContractStatus> contractStatusList = new ArrayList<>();

        boolean isAbort = false;

        // 先拉取更新合同
//        this.syncContractByJob();

        Date date = new Date();
        if (abortDate != null) {
            date = abortDate;
        }
        Date oneDayBefore = DateUtils.addDays(date, -1);
        if (orderId != null) {
            Result<OrderInfoVo> orderInfo = orderService.getOrderInfo(orderId);
            TransferContractStatusExample transferContractStatusExample = new TransferContractStatusExample();
            TransferContractStatusExample.Criteria criteria = transferContractStatusExample.createCriteria();
            // 补充合同要查出所有可能成功的合同
            criteria.andOrderIdEqualTo(orderId).andMerchantIdEqualTo(orderInfo.getModel().getMerchantId())
                    .andEndTimeGreaterThan(oneDayBefore).andStatusIn(TransferContractStatusEnum.maySuccessReport())
                    .andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
            contractStatusList = transferContractStatusMapper.selectByExample(transferContractStatusExample);

            if (CollectionUtils.isEmpty(contractStatusList)) {
                return false;
            }
            // 按订单查合同需要补充合同
            // contractList根据endTime排序 从大到小排序
            contractStatusList.sort((o1, o2) -> o2.getEndTime().compareTo(o1.getEndTime()));
            TransferContractStatus lastContract = contractStatusList.get(0);
            if (lastContract.getEndTime().before(date) && needSubmit) {
                // 补充合同
                Result<Boolean> result = this.submitContract(lastContract.getOrderId(), DateUtils.addMinutes(lastContract.getEndTime(), 1), date);
                logger.startLog().with("orderId", lastContract.getOrderId()).with("startTime", DateUtils.addMinutes(lastContract.getEndTime(), 1))
                        .with("endTime", date).with("result", result).log("违章转移 补充录入合同");
                return false;
            }

            // 不需要补充合同再过滤出需要终止的合同
            contractStatusList = contractStatusList.stream().filter(contract ->
                    TransferContractStatusEnum.needAbort().contains(contract.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contractStatusList)) {
                return false;
            }
        } else if (vehicleId != null) {
            Result<VehicleInfoVO> vehicleInfoResult = vehicleInfoService.getBaseById(vehicleId, true);
            if (ResultUtil.isModelNotNull(vehicleInfoResult)) {
                TransferContractStatusExample transferContractStatusExample = new TransferContractStatusExample();
                TransferContractStatusExample.Criteria criteria = transferContractStatusExample.createCriteria();
                criteria.andVehicleIdEqualTo(vehicleId).andMerchantIdEqualTo(vehicleInfoResult.getModel().getMerchantId())
                        .andEndTimeGreaterThan(date).andStatusIn(TransferContractStatusEnum.needAbort())
                        .andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
                contractStatusList = transferContractStatusMapper.selectByExample(transferContractStatusExample);
            }
        } else {
            throw new BizException("参数错误");
        }

        // 处理合同终止或作废
        Date finalDate = date;
        contractStatusList = contractStatusList.stream().filter(e-> e.getEndTime().after(finalDate)).collect(Collectors.toList());
        for (TransferContractStatus transferContractStatus : contractStatusList) {
            if (StringUtils.isEmpty(transferContractStatus.getContractId())) {
                logger.startLog().with("transferContractStatus", transferContractStatus).logError("合同orderId获取失败");
                continue;
            }
            
            // 处理单个合同的终止或作废
            Result<Boolean> booleanResult = processContractTermination(transferContractStatus, finalDate);
            isAbort = true;
            
            // 更新合同状态
            if (ResultUtil.isModelNotNull(booleanResult) && booleanResult.getModel()) {
                this.syncContractByJob();
            }
        }
        return isAbort;
    }

    @Override
    public void syncContractByJob() {
        // 获取本地合同最大版本
        TransferContractVersion oldVersion = transferContractVersionMapper.selectByPrimaryKey(1L);
        Long dbVersion = oldVersion.getLastVer();
        Result<List<SyncContractResponse>> listResult = remoteViolationService.syncContract(dbVersion);
        if (!listResult.isSuccess()) {
            logger.startLog().with("dbVersion", dbVersion).logError("违章转移 同步合同更新失败");
            return;
        }
        List<SyncContractResponse> list = listResult.getModel();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 优化去重逻辑：优先保留成功状态的合同，避免失败重试覆盖成功合同
        Map<String, SyncContractResponse> map = new HashMap<>();
        Long maxDbVersion = 0L;
        for (SyncContractResponse contractResponse : list) {
            if (contractResponse.getDbVersion() > maxDbVersion) {
                maxDbVersion = contractResponse.getDbVersion();
            }
            
            String contractNo = contractResponse.getContractNo();
            SyncContractResponse existingResponse = map.get(contractNo);
            
            if (existingResponse == null) {
                // 如果没有现有记录，直接添加
                map.put(contractNo, contractResponse);
            } else {
                // 如果已有记录，需要判断保留哪一个
                boolean shouldReplace = shouldReplaceContract(existingResponse, contractResponse);
                if (shouldReplace) {
                    map.put(contractNo, contractResponse);
                }
            }
        }

        // 更新本地合同最大版本
        TransferContractVersion updVersion = new TransferContractVersion();
        updVersion.setId(oldVersion.getId());
        updVersion.setLastVer(maxDbVersion);
        updVersion.setOpTime(System.currentTimeMillis());
        transferContractVersionMapper.updateByPrimaryKey(updVersion);

        TransferContractStatusExample transferContractStatusExample = new TransferContractStatusExample();
        transferContractStatusExample.createCriteria().andContractNoIn(new ArrayList<>(map.keySet()));
        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(transferContractStatusExample);

        for (TransferContractStatus transferContractStatus : contractStatusList) {
            SyncContractResponse syncContractResponse = map.get(transferContractStatus.getContractNo());
            // 已生效的合同只关注相同orderId的合同(避免重试的合同同步更新时覆盖成功结果)
            if (TransferContractStatusEnum.successReport().contains(transferContractStatus.getStatus())
                    && !String.valueOf(transferContractStatus.getContractId()).equals(syncContractResponse.getOrderId())) {
                continue;
            }
            // 更新合同状态
            TransferContractStatusParam param = TransferContractStatusParam.builder()
                    .id(transferContractStatus.getId())
                    .status(syncContractResponse.getState().byteValue())
                    .message(syncContractResponse.getPostMsg())
                    .postCode(syncContractResponse.getPostCode())
//                    .driverPhone(syncContractResponse.getDriverPhone())
                    .endTime(syncContractResponse.getEndTime())
                    .contractId(syncContractResponse.getOrderId())
                    .build();
            // 上报失败不更新endTime
            if (Integer.valueOf(TransferContractStatusEnum.REPORT_FAIL.getValue()).equals(syncContractResponse.getState())) {
                param.setEndTime(null);
            }
            this.saveTransferContractStatus(param);

            if (Integer.valueOf(TransferContractStatusEnum.REPORT_FAIL.getValue()).equals(syncContractResponse.getState())) {
                // 如果失败，发送企业微信
                if (transferContractStatus.getOrderId() != null && transferContractStatus.getOrderId() != 0L) {
                    // saas商户
                    String content = IllegalTransferMessageTemplate.formatSaasMerchantReportFail(
                            transferContractStatus.getMerchantId(), transferContractStatus.getOrderId(),
                            transferContractStatus.getCarNumber(), transferContractStatus.getContractNo(),
                            syncContractResponse.getOrderId(), syncContractResponse.getPostMsg());
                    enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, transferContractStatus.getMerchantId(), null);
                } else {
                    // 直连商家
                    String content = IllegalTransferMessageTemplate.formatDirectMerchantReportFail(
                            transferContractStatus.getMerchantId(), transferContractStatus.getCarNumber(),
                            transferContractStatus.getContractNo(), syncContractResponse.getOrderId(), syncContractResponse.getPostMsg());
                    enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, transferContractStatus.getMerchantId(), null);
                }
            } else if (Integer.valueOf(TransferContractStatusEnum.REPORTING.getValue()).equals(syncContractResponse.getState())
                    && syncContractResponse.getPostCode() != null && !"80000".equals(syncContractResponse.getPostCode())) {
                // 上报中 && postCode失败，也发送企业微信
                if (transferContractStatus.getOrderId() != null && transferContractStatus.getOrderId() != 0L) {
                    // saas商户
                    String content = IllegalTransferMessageTemplate.formatSaasMerchantReporting(
                            transferContractStatus.getMerchantId(), transferContractStatus.getOrderId(),
                            transferContractStatus.getCarNumber(), transferContractStatus.getContractNo(),
                            syncContractResponse.getOrderId(), syncContractResponse.getPostMsg());
                    enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, transferContractStatus.getMerchantId(), null);
                } else {
                    // 直连商家
                    String content = IllegalTransferMessageTemplate.formatDirectMerchantReporting(
                            transferContractStatus.getMerchantId(), transferContractStatus.getCarNumber(),
                            transferContractStatus.getContractNo(), syncContractResponse.getOrderId(), syncContractResponse.getPostMsg());
                    enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, transferContractStatus.getMerchantId(), null);
                }
            }

            // open调用的合同只更新合同表状态和发消息
            if (TransferContractBizTypeEnum.OPEN_API_CALL.getCode().equals(transferContractStatus.getBizType())) {
                continue;
            }

            // 更新违章的转移状态
            updIllegalTransferStatus(transferContractStatus.getVehicleId(), transferContractStatus.getOrderId(), transferContractStatus.getBeginTime(),
                    transferContractStatus.getEndTime(), syncContractResponse.getState());

        }
    }

    /**
     * 判断是否应该用新的合同记录替换现有的合同记录
     * 优先级规则：
     * 1. 优先保留成功状态的合同（state > 0）
     * 2. 如果都是成功状态或都是失败状态，选择dbVersion更大的
     * 3. 特殊处理：如果现有记录是成功的，新记录是失败的，不替换
     * 
     * @param existingResponse 现有记录
     * @param newResponse 新记录
     * @return true表示应该替换，false表示不替换
     */
    private boolean shouldReplaceContract(SyncContractResponse existingResponse, SyncContractResponse newResponse) {
        // 现有记录是成功的，新记录是失败的，不替换
        if (existingResponse.getState() > 0 && newResponse.getState() <= 0) {
            return false;
        }
        
        // 现有记录是失败的，新记录是成功的，替换
        if (existingResponse.getState() <= 0 && newResponse.getState() > 0) {
            return true;
        }
        
        // 如果状态相同（都成功或都失败），选择dbVersion更大的
        if (newResponse.getDbVersion() > existingResponse.getDbVersion()) {
            return true;
        }
        
        return false;
    }

    /***
     * 1.取车/取车后强排 如车辆有合同，终止生效的合同；录入合同 实际取车时间-订单最后结束时间 如最大时间大于1月 则实际取车时间-实际还车时间+1月
     * 2.还车 终止合同
     * 3.定时任务 每12小时执行一次
     *     扫描具有生效合同的订单，如订单未取车&&订单生效合同时间before当前时间24小时，录入合同 上一个合同结束时间-上一个订单结束时间+1月(为了减少表数据)
     */

    @Override
    public void submitDelayContractByJob() {
        /**
         * 每12小时执行一次
         * 扫描具有生效合同的订单，如订单为已取车&&订单生效合同时间before当前时间24小时 (转移订单需要有效)，录入合同 上一个合同结束时间+1分钟-上一个订单结束时间+x天
         * 续期时间不可小于1天 续期时间不可大于1月
         */

        Date nowDate = new Date();
        Date oneDayBefore = DateUtils.addDays(nowDate, -1);
        // 扫描已过期 但是不超过24小时的已上报合同
        TransferContractStatusExample contractStatusExample = new TransferContractStatusExample();
        contractStatusExample.createCriteria().andEndTimeGreaterThanOrEqualTo(oneDayBefore)
                .andStatusEqualTo(TransferContractStatusEnum.REPORTED.getValue())
                .andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
        Map<Long, Map<Long, List<TransferContractStatus>>> contractMap =
                transferContractStatusMapper.selectByExample(contractStatusExample).stream()
                        .collect(Collectors.groupingBy(TransferContractStatus::getMerchantId,
                                Collectors.groupingBy(TransferContractStatus::getOrderId)));
        logger.startLog().with("contractMap", contractMap).log("违章转移 合同续期");
        contractMap.forEach((merchantId, contractMapByOrder) -> {
            // 查询租车订单
            OrderInfoParam orderInfoParam = new OrderInfoParam();
            orderInfoParam.setIdList(new ArrayList<>(contractMapByOrder.keySet()));
            Result<List<OrderInfoVo>> orderListResult = orderSlaveService.getOrderBaseList(orderInfoParam);
            if (!orderListResult.isSuccess() || CollectionUtils.isEmpty(orderListResult.getModel())) {
                logger.startLog().with("merchantId", merchantId).logError("查询租车订单失败");
                return;
            }
            Map<Long, OrderInfoVo> orderMap = orderListResult.getModel().stream()
                    .collect(Collectors.toMap(OrderInfoVo::getId, e -> e, (v1, v2) -> v1));

            contractMapByOrder.forEach((orderId, contractList) -> {
                try {
                    OrderInfoVo orderInfoVo = orderMap.get(orderId);
                    // 过滤非已取车的订单和爬虫订单
                    if (orderInfoVo == null || !OrderStatusEnum.isPickedUp(orderInfoVo.getOrderStatus())
                            || CollectionUtils.isEmpty(contractList) || this.isReptileOrder(orderInfoVo.getExtra())) {
                        return;
                    }

                    // contractList根据endTime排序 从大到小排序
                    contractList.sort((o1, o2) -> o2.getEndTime().compareTo(o1.getEndTime()));
                    TransferContractStatus lastContract = contractList.get(0);

                    // 合同最后过期时间大于当前时间 不进行续期
                    if (lastContract.getEndTime().after(nowDate)) {
                        return;
                    }

                    // 按合同最后时间进行续期
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(lastContract.getEndTime());
                    calendar.add(Calendar.MINUTE, 1);
                    Date startTime = calendar.getTime();
                    calendar.add(Calendar.DAY_OF_MONTH, renewalDays);
                    Date endTime = calendar.getTime();
                    Result<Boolean> result = this.submitContract(orderId, startTime, endTime);
                    logger.startLog().with("orderId", orderId).with("startTime", startTime).with("endTime", endTime)
                            .with("result", result).log("违章转移 合同续期");
                } catch (Exception e) {
                    // 可能是三方接口超时导致的异常
                    logger.startLog().with("orderId", orderId).with("contractList", contractList).logError("违章转移 合同续期异常", e);
                    return;
                }

            });
        });
    }

    @Override
    public Result<Boolean> cancelContract(String contractId) {
        logger.startLog().with("contractId", contractId).logAudit("违章转移 作废合同");
        CancelContractReq cancelContractReq = new CancelContractReq();
        cancelContractReq.setOrderId(contractId);
        Result<Boolean> result = remoteViolationService.cancelContract(cancelContractReq);
        this.syncContractByJob();
        return result;
    }

    public boolean isReptileOrder(String extra) {
        if (StringUtils.isBlank(extra)) {
            return false;
        }
        JSONObject jsonObject = JSON.parseObject(extra);
        Byte isReptile = jsonObject.getByte("isReptile");
        return YesOrNoEnum.isYes(isReptile);
    }

    @Override
    public TransferReportSummaryDTO getTransferReportSummary(Date startTime, Date endTime) {
        try {
            log.info("开始统计转移上报情况，时间范围：{} - {}", startTime, endTime);
            
            // 查询指定时间范围内的转移合同状态数据
            TransferContractStatusExample example = new TransferContractStatusExample();
            example.createCriteria()
                    .andOpTimeBetween(startTime.getTime(), endTime.getTime());
            example.setOrderByClause("op_time desc");
            
            List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(example);
            
            TransferReportSummaryDTO summaryDTO = new TransferReportSummaryDTO();
            // 设置统计时间段，格式：2024-01-15 06:00 ~ 2024-01-16 06:00
            String reportDateRange = String.format("%tF %tR ~ %tF %tR", 
                    startTime, startTime, endTime, endTime);
            summaryDTO.setReportDate(reportDateRange);
            summaryDTO.setTotalCount(contractStatusList.size());
            
            if (CollectionUtils.isEmpty(contractStatusList)) {
                summaryDTO.setSuccessCount(0);
                summaryDTO.setReportList(new ArrayList<>());
                return summaryDTO;
            }
            
            // 统计成功数量
            int successCount = 0;
            List<TransferReportDTO> reportList = new ArrayList<>();
            
            // 获取商家信息Map
            Set<Long> merchantIds = contractStatusList.stream()
                    .map(TransferContractStatus::getMerchantId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<Long, String> merchantNameMap = getMerchantNameMap(merchantIds);
            
            for (TransferContractStatus contractStatus : contractStatusList) {
                TransferReportDTO reportDTO = new TransferReportDTO();
                
                // 基本信息
                reportDTO.setOrderId(contractStatus.getOrderId());
                reportDTO.setCarNumber(contractStatus.getCarNumber());
                reportDTO.setContractNo(contractStatus.getContractNo());
                reportDTO.setContractId(contractStatus.getContractId());
                reportDTO.setContractTime(contractStatus.getBeginTime());
                reportDTO.setContractEndTime(contractStatus.getEndTime());
                reportDTO.setDriverName(contractStatus.getDriverName());
                reportDTO.setDriverId(contractStatus.getDriverId());
                reportDTO.setAccount(contractStatus.getAccount());
                reportDTO.setErrorMsg(contractStatus.getErrorMsg());
                reportDTO.setPostMsg(contractStatus.getMessage());
                
                // 时间转换
                reportDTO.setCreateTime(new Date(contractStatus.getCreateTime()));
                reportDTO.setUpdateTime(new Date(contractStatus.getOpTime()));
                
                // 商家名称
                String merchantName = merchantNameMap.get(contractStatus.getMerchantId());
                reportDTO.setMerchantName(StringUtils.isNotBlank(merchantName) ? merchantName : "未知商家");
                reportDTO.setMerchantId(contractStatus.getMerchantId().toString());
                
                // 转移状态
                String transferStatus = getTransferStatusDesc(contractStatus.getStatus());
                reportDTO.setTransferStatus(transferStatus);
                
                // 统计成功数量
                if (TransferContractStatusEnum.successReport().contains(contractStatus.getStatus())) {
                    successCount++;
                }
                
                reportList.add(reportDTO);
            }
            
            summaryDTO.setSuccessCount(successCount);
            summaryDTO.setReportList(reportList);
            
            log.info("转移上报统计完成，总数：{}，成功：{}", summaryDTO.getTotalCount(), successCount);
            return summaryDTO;
            
        } catch (Exception e) {
            log.error("统计转移上报情况失败", e);
            throw new BizException("统计转移上报情况失败：" + e.getMessage());
        }
    }

    @Override
    public void executeTransferReportTask() {
        try {
            log.info("开始执行转移上报统计任务");
            
            // 计算昨天早上6点到今天早上6点的时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 6);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date endTime = calendar.getTime(); // 今天早上6点
            
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date startTime = calendar.getTime(); // 昨天早上6点
            
            // 获取统计数据
            TransferReportSummaryDTO summaryDTO = getTransferReportSummary(startTime, endTime);
            
            // 发送企业微信消息
            sendTransferReportToWechat(summaryDTO);
            
            log.info("转移上报统计任务执行完成");
            
        } catch (Exception e) {
            log.error("执行转移上报统计任务失败", e);
        }
    }
    
    /**
     * 发送转移上报统计数据到企业微信
     */
    private void sendTransferReportToWechat(TransferReportSummaryDTO summaryDTO) {
        File excelFile = null;
        try {
            // 生成Excel文件
            String fileName = "转移上报统计报告_" + new SimpleDateFormat("yyyyMMdd_HHmm").format(new Date());
            
            // 转换数据为Excel DTO
            List<TransferReportExcelDTO> excelDataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(summaryDTO.getReportList())) {
                for (TransferReportDTO reportDTO : summaryDTO.getReportList()) {
                    TransferReportExcelDTO excelDTO = new TransferReportExcelDTO();
                    excelDTO.setOrderId(reportDTO.getOrderId() != null ? reportDTO.getOrderId().toString() : "");
                    excelDTO.setMerchantName(reportDTO.getMerchantName());
                    excelDTO.setMerchantId(reportDTO.getMerchantId());
                    excelDTO.setCarNumber(reportDTO.getCarNumber());
                    excelDTO.setContractNo(reportDTO.getContractNo());
                    excelDTO.setContractId(reportDTO.getContractId());
                    excelDTO.setContractTime(reportDTO.getContractTime() != null ? 
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportDTO.getContractTime()) : "");
                    excelDTO.setContractEndTime(reportDTO.getContractEndTime() != null ? 
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportDTO.getContractEndTime()) : "");
                    excelDTO.setDriverName(reportDTO.getDriverName());
                    excelDTO.setDriverId(reportDTO.getDriverId());
                    excelDTO.setAccount(reportDTO.getAccount());
                    excelDTO.setTransferStatus(reportDTO.getTransferStatus());
                    excelDTO.setCreateTime(reportDTO.getCreateTime() != null ? 
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportDTO.getCreateTime()) : "");
                    excelDTO.setUpdateTime(reportDTO.getUpdateTime() != null ? 
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportDTO.getUpdateTime()) : "");
                    excelDTO.setErrorMsg(reportDTO.getErrorMsg());
                    excelDTO.setPostMsg(reportDTO.getPostMsg());
                    
                    excelDataList.add(excelDTO);
                }
            }
            
            // 生成Excel文件
            excelFile = EasyExcelUtils.downAndGetFile(excelDataList, TransferReportExcelDTO.class, fileName);
            
            // 发送文本消息说明
            String message = String.format("转移上报统计报告\n统计时间段：%s\n总记录数：%d\n成功数量：%d",
                    summaryDTO.getReportDate(),
                    summaryDTO.getTotalCount(),
                    summaryDTO.getSuccessCount());
            
            enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_MONITOR_ROBOT_KEY, message, null, null);
            
            // 发送Excel文件
            if (excelFile != null && excelFile.exists()) {
                enterpriseWechatService.sendGroupFileMsg(QyWechatConstant.VEHICLE_TRANSFER_MONITOR_ROBOT_KEY, 
                        excelFile.getAbsolutePath(), fileName + ".xlsx", null);
                log.info("转移上报统计Excel文件已发送到企业微信，文件名：{}", fileName);
            }
            
        } catch (Exception e) {
            log.error("发送转移上报统计数据到企业微信失败", e);
        } finally {
            // 删除临时文件
            if (excelFile != null && excelFile.exists()) {
                try {
                    boolean deleted = excelFile.delete();
                    log.info("删除临时Excel文件：{}，结果：{}", excelFile.getAbsolutePath(), deleted);
                } catch (Exception e) {
                    log.error("删除临时Excel文件失败：{}", excelFile.getAbsolutePath(), e);
                }
            }
        }
    }
    
    /**
     * 获取商家名称映射
     */
    private Map<Long, String> getMerchantNameMap(Set<Long> merchantIds) {
        Map<Long, String> merchantNameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(merchantIds)) {
            return merchantNameMap;
        }
        
        try {
            for (Long merchantId : merchantIds) {
                Result<MerchantInfoVo> result = merchantInfoService.findById(merchantId);
                if (ResultUtil.isResultSuccess(result) && result.getModel() != null) {
                    merchantNameMap.put(merchantId, result.getModel().getName());
                }
            }
        } catch (Exception e) {
            log.error("获取商家名称映射失败", e);
        }
        
        return merchantNameMap;
    }

    @Override
    public void executeViolationMonthlyBillTask() {
        try {
            log.info("开始执行违章月度账单推送任务");
            
            // 查询明天即将到期的套餐订单（套餐结束前一天触发）
            Date tomorrow = DateUtils.addDays(new Date(), 1);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(tomorrow);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date tomorrowStart = calendar.getTime();
            
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date dayAfterTomorrowStart = calendar.getTime();
            
            // 查询明天到期的违章转移套餐订单
            MallServiceOrderParam param = new MallServiceOrderParam();
            param.setOrderStatus(MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue());
            param.setItemType(MallOrderItemEnum.ILLEGAL_SEARCH.getItemType());
            param.setStartExpirationDate(tomorrowStart);
            param.setEndExpirationDate(dayAfterTomorrowStart);
            
            // 获取所有商家的套餐订单
            List<MallServiceOrderInfoDTO> expiringOrders = mallServiceOrderInfoService.serviceOrderList(null, param);

            if (CollectionUtils.isEmpty(expiringOrders)) {
                return;
            }
            
            // 按商家分组处理
            Map<Long, List<MallServiceOrderInfoDTO>> ordersByMerchant = expiringOrders.stream()
                    .filter(order -> MallServiceItemConstant.ILLEGAL.TRANSFER.equals(order.getItemSubPackage()))
                    .collect(Collectors.groupingBy(MallServiceOrderInfoDTO::getMerchantId));
            
            for (Map.Entry<Long, List<MallServiceOrderInfoDTO>> entry : ordersByMerchant.entrySet()) {
                Long merchantId = entry.getKey();
                List<MallServiceOrderInfoDTO> merchantOrders = entry.getValue();
                
                try {
                    generateAndSendViolationMonthlyBill(merchantId, merchantOrders);
                } catch (Exception e) {
                    log.error("生成商家{}违章月度账单失败", merchantId, e);
                }
            }
            
            log.info("违章月度账单推送任务执行完成");
            
        } catch (Exception e) {
            log.error("执行违章月度账单推送任务失败", e);
        }
    }
    
    /**
     * 生成并发送违章月度账单
     */
    private void generateAndSendViolationMonthlyBill(Long merchantId, List<MallServiceOrderInfoDTO> orders) {
        File excelFile = null;
        try {
            // 获取商家信息
            Result<MerchantInfoVo> merchantResult = merchantInfoService.findById(merchantId);
            String merchantName = "未知商家";
            if (ResultUtil.isResultSuccess(merchantResult) && merchantResult.getModel() != null) {
                merchantName = merchantResult.getModel().getName();
            }
            
            List<ViolationMonthlyBillExcelDTO> billDataList = new ArrayList<>();
            
            for (MallServiceOrderInfoDTO order : orders) {
                ViolationMonthlyBillExcelDTO billDTO = new ViolationMonthlyBillExcelDTO();
                billDTO.setMerchantName(merchantName);
                billDTO.setMerchantId(merchantId.toString());
                billDTO.setPackageName(order.getItemName());
                
                // 计算套餐时间期间
                Date createTime = new Date(order.getCreateTime());
                Date expirationDate = order.getExpirationDate();
                String packagePeriod = String.format("%s ~ %s", 
                        new SimpleDateFormat("yyyy-MM-dd").format(createTime),
                        new SimpleDateFormat("yyyy-MM-dd").format(expirationDate));
                billDTO.setPackagePeriod(packagePeriod);
                
                // 查询该商家在套餐期间绑定车辆的违章数据
                ViolationTransferSummaryDTO violationTransferSummaryDTO = getViolationStatistics(merchantId, createTime, expirationDate);
                
                billDTO.setTotalViolationCount(violationTransferSummaryDTO.getTotalViolationCount());
                billDTO.setReportedCount(violationTransferSummaryDTO.getReportedCount());
                billDTO.setSuccessReportCount(violationTransferSummaryDTO.getSuccessReportCount());
                billDTO.setFailReportCount(violationTransferSummaryDTO.getFailReportCount());
                billDTO.setSuccessTransferCount(violationTransferSummaryDTO.getSuccessTransferCount());
                
                billDataList.add(billDTO);
            }
            
            if (CollectionUtils.isEmpty(billDataList)) {
                log.info("商家{}没有违章月度账单数据", merchantId);
                return;
            }
            
            // 生成Excel文件
            String fileName = String.format("违章月度账单_%s_%s", merchantName, 
                    new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()));
            
            excelFile = EasyExcelUtils.downAndGetFile(billDataList, ViolationMonthlyBillExcelDTO.class, fileName);

            // 发送Excel文件
            if (excelFile != null && excelFile.exists()) {
                enterpriseWechatService.sendGroupFileMsg(QyWechatConstant.VEHICLE_TRANSFER_MONITOR_ROBOT_KEY,
                        excelFile.getAbsolutePath(), fileName + ".xlsx", merchantId);
                log.info("违章月度账单Excel文件已发送到企业微信，商家：{}，文件名：{}", merchantName, fileName);
            }
            
        } catch (Exception e) {
            log.error("生成商家{}违章月度账单失败", merchantId, e);
        } finally {
            // 删除临时文件
            if (excelFile != null && excelFile.exists()) {
                try {
                    boolean deleted = excelFile.delete();
                    log.info("删除临时Excel文件：{}，结果：{}", excelFile.getAbsolutePath(), deleted);
                } catch (Exception e) {
                    log.error("删除临时Excel文件失败：{}", excelFile.getAbsolutePath(), e);
                }
            }
        }
    }
    
    /**
     * 获取违章统计数据
     */
    private ViolationTransferSummaryDTO getViolationStatistics(Long merchantId, Date startTime, Date endTime) {
        ViolationTransferSummaryDTO statistics = new ViolationTransferSummaryDTO();
        
        try {
            // 查询该商家在套餐期间的转移合同
            TransferContractStatusExample contractExample = new TransferContractStatusExample();
            contractExample.createCriteria()
                    .andMerchantIdEqualTo(merchantId)
                    .andBeginTimeBetween(startTime, endTime)
                    .andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
                    
            List<TransferContractStatus> contracts = transferContractStatusMapper.selectByExample(contractExample);
            
            if (CollectionUtils.isEmpty(contracts)) {
                return statistics;
            }
            
            // 统计违章数据
            int totalViolationCount = 0;
            int reportedCount = 0;
            int successReportCount = 0;
            int failReportCount = 0;
            int successTransferCount = 0;
            
            for (TransferContractStatus contract : contracts) {
                // 查询该合同期间的违章数据
                List<VehicleIllegalOrderVO> illegalOrders = vehicleIllegalOrderService.getBaseVehicleIllegalOrder(
                        VehicleIllegalOrderInnerQuery.builder()
                                .vehicleId(contract.getVehicleId())
                                .orderId(contract.getOrderId())
                                .illegalStartTime(contract.getBeginTime())
                                .illegalEndTime(contract.getEndTime())
                                .build());
                
                totalViolationCount += illegalOrders.size();
                
                // 统计上报情况
                if (contract.getStatus() != null) {
                    reportedCount++;
                    if (TransferContractStatusEnum.successReport().contains(contract.getStatus())) {
                        successReportCount++;
                        // 成功上报的合同中包含的违章数量即为成功转移的违章数量
                        successTransferCount += illegalOrders.size();
                    } else if (Objects.equals(TransferContractStatusEnum.REPORT_FAIL.getValue(), contract.getStatus())) {
                        failReportCount++;
                    }
                }
            }
            
            statistics.setTotalViolationCount(totalViolationCount);
            statistics.setReportedCount(reportedCount);
            statistics.setSuccessReportCount(successReportCount);
            statistics.setFailReportCount(failReportCount);
            statistics.setSuccessTransferCount(successTransferCount);
            
        } catch (Exception e) {
            log.error("获取商家{}违章统计数据失败", merchantId, e);
        }
        
        return statistics;
    }
    


    /**
     * 获取转移状态描述
     */
    private String getTransferStatusDesc(Byte status) {
        if (status == null) {
            return "未知状态";
        }
        
        if (Objects.equals(TransferContractStatusEnum.PLAN_REPORT.getValue(), status)) {
            return "计划上报";
        } else if (Objects.equals(TransferContractStatusEnum.REPORT_FAIL.getValue(), status)) {
            return "上报失败";
        } else if (Objects.equals(TransferContractStatusEnum.REPORTING.getValue(), status)) {
            return "上报中";
        } else if (Objects.equals(TransferContractStatusEnum.REPORTED.getValue(), status)) {
            return "已上报";
        } else if (Objects.equals(TransferContractStatusEnum.ABORTED.getValue(), status)) {
            return "已终止";
        } else if (Objects.equals(TransferContractStatusEnum.CANCELLED.getValue(), status)) {
            return "已取消";
        } else {
            return "未知状态";
        }
    }

    /**
     * 处理合同终止或作废逻辑
     * 根据合同开始时间与当前时间比较，决定是作废合同还是终止合同
     * 
     * @param transferContractStatus 合同状态信息
     * @param currentTime 当前时间
     * @return 处理结果
     */
    private Result<Boolean> processContractTermination(TransferContractStatus transferContractStatus, Date currentTime) {
        String contractId = transferContractStatus.getContractId();
        Date beginTime = transferContractStatus.getBeginTime();
        
        Result<Boolean> result;
        
        if (beginTime.after(currentTime)) {
            // 合同开始时间 > 当前时间，作废合同
            CancelContractReq cancelContractReq = new CancelContractReq();
            cancelContractReq.setOrderId(contractId);
            result = remoteViolationService.cancelContract(cancelContractReq);
        } else {
            // 合同开始时间 <= 当前时间，终止合同
            AbortContractReq abortContractReq = new AbortContractReq();
            abortContractReq.setOrderId(contractId);
            result = remoteViolationService.abortContract(abortContractReq);
            
            // 终止合同成功时，更新合同结束时间为当前时间
            if (ResultUtil.isModelNotNull(result) && result.getModel()) {
                updateContractEndTime(transferContractStatus, currentTime);
            }
        }
        
        return result;
    }
    
    /**
     * 更新合同结束时间为当前时间
     * 
     * @param transferContractStatus 合同状态信息
     * @param currentTime 当前时间
     */
    private void updateContractEndTime(TransferContractStatus transferContractStatus, Date currentTime) {
        try {
            TransferContractStatusParam updateParam = new TransferContractStatusParam();
            updateParam.setId(transferContractStatus.getId());
            updateParam.setEndTime(currentTime);
            this.saveTransferContractStatus(updateParam);
        } catch (Exception e) {
            logger.startLog()
                    .with("contractId", transferContractStatus.getContractId())
                    .logError("违章转移 更新合同结束时间失败", e);
        }
    }

    /**
     * 违章登录成功后自动重试失败的合同
     * @param account 122账号
     */
    public void retryFailedContractsOnLoginSuccess(String account) {
        asyncPromiseExecutor.execute(() -> {
            retryFailedContractsAsync(account);
        });
    }

    /**
     * 异步重试失败的合同
     * @param account 122账号
     */
    private void retryFailedContractsAsync(String account) {
        logger.startLog().with("account", account).log("开始异步重试失败合同");
        
        try {

            // todo 先触发一遍拉取
            syncContractByJob();

            // 分页查询可重试的合同，每页处理100条
            int pageSize = 100;
            int offset = 0;
            boolean hasMore = true;
            
            while (hasMore) {
                List<TransferContractStatusVo> retryableContracts = queryRetryableContracts(account, offset, pageSize);
                
                if (CollectionUtils.isEmpty(retryableContracts)) {
                    hasMore = false;
                    break;
                }
                
                // 分批处理每个合同
                for (TransferContractStatusVo contract : retryableContracts) {
                    try {
                        retryContract(contract);
                    } catch (Exception e) {
                        logger.startLog()
                            .with("contractId", contract.getContractId())
                            .with("account", account)
                            .logError("重试合同失败", e);
                    }
                }
                
                // 如果查询结果少于页面大小，说明已经是最后一页
                if (retryableContracts.size() < pageSize) {
                    hasMore = false;
                } else {
                    offset += pageSize;
                }
            }
            
            logger.startLog().with("account", account).log("异步重试失败合同完成");
            
        } catch (Exception e) {
            logger.startLog().with("account", account).logError("异步重试失败合同异常", e);
        }
    }

    /**
     * 查询可重试的合同
     * @param account 122账号
     * @param offset 偏移量
     * @param pageSize 页面大小
     * @return 可重试的合同列表
     */
    private List<TransferContractStatusVo> queryRetryableContracts(String account, int offset, int pageSize) {
        try {
            TransferContractStatusExample example = new TransferContractStatusExample();
            TransferContractStatusExample.Criteria criteria = example.createCriteria();
            
            // 查询指定账号的合同
            criteria.andAccountEqualTo(account);
            
            // 状态为失败
            criteria.andStatusEqualTo(TransferContractStatusEnum.REPORT_FAIL.getValue());
            
            // 结束时间大于当前时间（最小单位：分钟）
            Date currentTime = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentTime);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date currentMinute = calendar.getTime();
            criteria.andEndTimeGreaterThan(currentMinute);

            // saas内部的合同
            criteria.andBizTypeEqualTo(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
            
            // 可重试的错误码：无在线客户端、客户端不可用、客户端执行超时、合同开始时间错误
            List<String> retryablePostCodes = ViolationResultCodeEnum.TransferContractEnum.getRetryablePostCodes();
            criteria.andPostCodeIn(retryablePostCodes);
            
            // 分页和排序
            example.setOrderByClause(String.format("id ASC LIMIT %d OFFSET %d", pageSize, offset));
            
            List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(example);
            
            // 直接转换为VO，不需要额外过滤
            List<TransferContractStatusVo> retryableContracts = new ArrayList<>();
            for (TransferContractStatus contractStatus : contractStatusList) {
                TransferContractStatusVo vo = new TransferContractStatusVo();
                BeanUtils.copyProperties(contractStatus, vo);
                retryableContracts.add(vo);
            }
            
            return retryableContracts;
            
        } catch (Exception e) {
            logger.startLog()
                .with("account", account)
                .with("offset", offset)
                .with("pageSize", pageSize)
                .logError("查询可重试合同失败", e);
            return new ArrayList<>();
        }
    }



    /**
     * 重试单个合同
     * @param contract 要重试的合同
     */
    private void retryContract(TransferContractStatusVo contract) {
        logger.startLog()
            .with("contractId", contract.getContractId())
            .with("orderId", contract.getOrderId())
            .log("开始重试合同");
        
        try {
            // 更新开始时间为当前时间
            Date currentTime = new Date();

            // 上报前判断合同是否已有重叠、可能上报成功、同一辆车、同一个订单、同一个商家的合同
            if (hasOverlappingContract(contract.getMerchantId(), contract.getVehicleId(), contract.getOrderId(), currentTime, contract.getEndTime())) {
                return;
            }

            // 调用submitOneContract方法进行重新上报
            Result<Boolean> result = this.submitOneContract(contract.getOrderId(), currentTime, contract.getEndTime(), contract.getVehicleId());

            logger.startLog()
                    .with("contractId", contract.getContractId())
                    .with("orderId", contract.getOrderId())
                    .with("result", result)
                    .log("业务重试合同");

        } catch (Exception e) {
            logger.startLog()
                    .with("contractId", contract.getContractId())
                    .with("orderId", contract.getOrderId())
                    .logError("重试合同异常", e);
        }
    }

    /**
     * 检查是否存在重叠的合同
     * @param merchantId 商家ID
     * @param vehicleId 车辆ID
     * @param orderId 订单ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return true表示存在重叠合同
     */
    private boolean hasOverlappingContract(Long merchantId, Long vehicleId, Long orderId, Date beginTime, Date endTime) {
        try {
            TransferContractStatusParam contractStatusParam = new TransferContractStatusParam();
            contractStatusParam.setMerchantId(merchantId);
            contractStatusParam.setVehicleId(vehicleId);
            contractStatusParam.setOrderId(orderId);
            contractStatusParam.setStartEndTime(beginTime);
            contractStatusParam.setEndBeginTime(endTime);
            contractStatusParam.setStatusList(TransferContractStatusEnum.repeatReport());
            contractStatusParam.setBizType(TransferContractBizTypeEnum.SAAS_INTERNAL_PURCHASE.getCode());
            
            Result<List<TransferContractStatusVo>> transferContractStatusList = this.getTransferContractStatus(contractStatusParam);

            return transferContractStatusList.isSuccess() && CollectionUtils.isNotEmpty(transferContractStatusList.getModel());

        } catch (Exception e) {
            logger.startLog()
                .with("merchantId", merchantId)
                .with("vehicleId", vehicleId)
                .with("orderId", orderId)
                .logError("检查重叠合同异常", e);
            return true;
        }
    }

}
