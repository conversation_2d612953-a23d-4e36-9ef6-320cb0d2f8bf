package com.ql.rent.provider.pay;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.*;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.github.rholder.retry.*;
import com.ql.dto.pay.*;
import com.ql.enums.ThirdAliPayEnum;
import com.ql.enums.ThirdPayEnum;
import com.ql.rent.config.pay.AliPayConfig;
import com.ql.rent.dao.bill.ThirdPayMapper;
import com.ql.rent.dao.bill.ThirdRefundPayMapper;
import com.ql.rent.dao.bill.ex.ThirdPayMapperEx;
import com.ql.rent.entity.bill.ThirdPay;
import com.ql.rent.entity.bill.ThirdRefundPay;
import com.ql.rent.enums.merchant.RoyaltyRelationTypeEnum;
import com.ql.rent.param.merchant.RoyaltyRelationParam;
import com.ql.rent.param.pay.*;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.util.AmountUtil;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AliPayHandler implements ThirdPayHandler {
    @Resource
    private AlipayClient aliPayPayClient;
    @Resource
    private AliPayConfig aliPayConfig;
    @Resource
    private ThirdPayMapper thirdPayMapper;
    @Resource
    private ThirdRefundPayMapper thirdRefundPayMapper;
    @Resource
    private ThirdPayMapperEx thirdPayMapperEx;
    @Resource
    private Executor asyncPromiseExecutor;

    @NotNull
    @Override
    @WithSpan("[alipay]创建交易单")
    public ThirdPayResult createThirdPayOrder(ThirdPayCreateParam param) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);
            // 构造请求参数以调用接口
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();

            // 设置商户订单号
            model.setOutTradeNo(param.getPayNo());

            // 设置订单总金额
            model.setTotalAmount(AmountUtil.amount2BigDecimal(param.getAmount()).toString());

            // 设置订单标题
            model.setSubject(param.getSubject());

            // 设置产品码
            model.setProductCode("QR_CODE_OFFLINE");

            request.setBizModel(model);

            request.setNotifyUrl(aliPayConfig.getNotifyUrl());
            spanEnhancer.withJson("request", request);
            AlipayTradePrecreateResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return ThirdPayResult.failedResult(response.getSubCode());
            }

            return ThirdPayResult.builder()
                    .success(true)
                    .qrCode(response.getQrCode())
                    .build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return ThirdPayResult.failedResult();
        }
    }

    @NotNull
    @Override
    @WithSpan("[alipay]退款")
    public RefundResult refund(RefundParam param) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);
            // 构造请求参数以调用接口
            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();

            // 设置支付宝交易号
            model.setTradeNo(param.getThirdSourceNo());

            // 设置退款金额
            model.setRefundAmount(AmountUtil.amount2BigDecimal(param.getAmount()).toString());

            // 设置退款请求号
            model.setOutRequestNo(param.getRefundNo());

//            RefundParam.RefundRoyaltyDTO refundRoyaltyDTO = param.getRefundRoyaltyDTO();
//            if (refundRoyaltyDTO != null) {
//                // 设置退分账明细信息
//                List<OpenApiRoyaltyDetailInfoPojo> refundRoyaltyParameters = new ArrayList<OpenApiRoyaltyDetailInfoPojo>();
//                OpenApiRoyaltyDetailInfoPojo refundRoyaltyParameters0 = new OpenApiRoyaltyDetailInfoPojo();
//                refundRoyaltyParameters0.setRoyaltyType("transfer");
//                refundRoyaltyParameters0.setAmount(String.valueOf(AmountUtil.amount2BigDecimal(refundRoyaltyDTO.getRoyaltyAmount())));
//                refundRoyaltyParameters0.setTransOut(refundRoyaltyDTO.getTransOutAccount());
//                refundRoyaltyParameters0.setTransOutType(getAliRoyaltyType(refundRoyaltyDTO.getTransOutType()));
//                refundRoyaltyParameters0.setDesc("退分账");
//                refundRoyaltyParameters.add(refundRoyaltyParameters0);
//                model.setRefundRoyaltyParameters(refundRoyaltyParameters);
//            }

            // 设置查询选项
            List<String> queryOptions = new ArrayList<>();
            queryOptions.add("refund_detail_item_list");
            model.setQueryOptions(queryOptions);

            request.setBizModel(model);
            spanEnhancer.withJson("request", request);
            AlipayTradeRefundResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return RefundResult.failedResult(response.getSubCode());
            }

            return RefundResult.builder()
                    .success(true)
                    .fundChange(response.getFundChange().equals("Y"))
                    .actualAmount(AmountUtil.amount2Long(response.getSendBackFee()))
                    .build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return RefundResult.failedResult();
        }
    }

    @Override
    public void postToRefund(PostRefundParam postRefundParam) {
        // 退款接口成功但退款金额没有变动才继续
        if (postRefundParam.getRefundResult() == null ||
                !(BooleanUtils.isTrue(postRefundParam.getRefundResult().getSuccess()) && BooleanUtils.isFalse(postRefundParam.getRefundResult().getFundChange()))) {
            return;
        }
        asyncPromiseExecutor.execute(() -> {
            try {
                Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
                        .retryIfExceptionOfType(Exception.class)
                        .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 4, TimeUnit.SECONDS))
                        .retryIfResult(resp -> resp == null || BooleanUtils.isFalse(resp))
                        .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                        .build();

                retryer.call(() -> {
                    log.info("支付宝进一步确认退款 postRefundParam={}", JSON.toJSONString(postRefundParam));
                    ThirdRefundPay thirdRefundPay = thirdRefundPayMapper.selectByPrimaryKey(postRefundParam.getRefundPayId());
                    if (thirdRefundPay == null) {
                        return true;
                    }
                    if (!thirdRefundPay.getStatus().equals(ThirdPayEnum.ThirdRefundPayStatus.REFUNDING.getStatus())) {
                        return true;
                    }
                    ThirdPay thirdPay = thirdPayMapper.selectByPrimaryKey(thirdRefundPay.getPayId());
                    if (thirdPay == null) {
                        return true;
                    }
                    RefundQueryParam refundQueryParam = RefundQueryParam.builder()
                            .thirdPaySource(thirdPay.getThirdSource())
                            .payNo(thirdPay.getPayNo())
                            .refundNo(thirdRefundPay.getRefundNo())
                            .build();
                    RefundQueryResult refundQueryResult = this.refundQuery(refundQueryParam);
                    if (refundQueryResult.getSuccess() && refundQueryResult.getFundChange()) {
                        ThirdRefundPay updateRefundPay = new ThirdRefundPay();
                        updateRefundPay.setId(thirdRefundPay.getId());
                        updateRefundPay.setActualAmount(refundQueryResult.getRefundAmount());
                        updateRefundPay.setStatus(ThirdPayEnum.ThirdRefundPayStatus.SUCCESS.getStatus());
                        updateRefundPay.setSourceFailedReason(refundQueryResult.getSourceFailedReason());
                        updateRefundPay.setPayOpTime(refundQueryResult.getGmtRefundPay());
                        updateRefundPay.setPayTime(refundQueryResult.getGmtRefundPay());
                        updateRefundPay.setOpTime(System.currentTimeMillis());
                        updateRefundPay.setLastVer(updateRefundPay.getLastVer() + 1);
                        thirdRefundPayMapper.updateByPrimaryKeySelective(updateRefundPay);
                        thirdPayMapperEx.updateRefundAmount(thirdPay.getId(), refundQueryResult.getRefundAmount(), System.currentTimeMillis());
                        return true;
                    } else {
                        return false;
                    }
                });
            } catch (Exception e) {
                log.error("支付宝进一步确认退款 失败 postRefundParam={}", JSON.toJSONString(postRefundParam), e);
            }
        });
    }

    @NotNull
    @Override
    @WithSpan("[alipay]绑定分账关系")
    public RoyaltyResult royaltyBind(RoyaltyRelationParam royaltyRelationParam) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);

            AlipayTradeRoyaltyRelationBindRequest request = new AlipayTradeRoyaltyRelationBindRequest();
            AlipayTradeRoyaltyRelationBindModel model = new AlipayTradeRoyaltyRelationBindModel();
            model.setOutRequestNo(UuidUtil.getUUID());
            List<RoyaltyEntity> receiverList = new ArrayList<RoyaltyEntity>();
            RoyaltyEntity receiverList0 = new RoyaltyEntity();
            receiverList0.setName(royaltyRelationParam.getName());
            receiverList0.setType(getAliRoyaltyType(royaltyRelationParam.getType()));
            receiverList0.setAccount(royaltyRelationParam.getAccount());
            receiverList.add(receiverList0);
            model.setReceiverList(receiverList);
            request.setBizModel(model);

            spanEnhancer.withJson("request", request);
            AlipayTradeRoyaltyRelationBindResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return RoyaltyResult.failedResult(response.getSubCode());
            }

            return RoyaltyResult.builder()
                    .success(true)
                    .build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return RoyaltyResult.failedResult();
        }
    }

    @NotNull
    @Override
    @WithSpan("[alipay]解绑分账关系")
    public RoyaltyResult royaltyUnbind(RoyaltyRelationParam royaltyRelationParam) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);

            AlipayTradeRoyaltyRelationUnbindRequest request = new AlipayTradeRoyaltyRelationUnbindRequest();
            AlipayTradeRoyaltyRelationBindModel model = new AlipayTradeRoyaltyRelationBindModel();
            model.setOutRequestNo(UuidUtil.getUUID());
            List<RoyaltyEntity> receiverList = new ArrayList<RoyaltyEntity>();
            RoyaltyEntity receiverList0 = new RoyaltyEntity();
            receiverList0.setType(getAliRoyaltyType(royaltyRelationParam.getType()));
            receiverList0.setName(royaltyRelationParam.getName());
            receiverList0.setAccount(royaltyRelationParam.getAccount());
            receiverList.add(receiverList0);
            model.setReceiverList(receiverList);
            request.setBizModel(model);

            spanEnhancer.withJson("request", request);
            AlipayTradeRoyaltyRelationUnbindResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return RoyaltyResult.failedResult(response.getSubCode());
            }

            return RoyaltyResult.builder()
                    .success(true)
                    .build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return RoyaltyResult.failedResult();
        }
    }

    private static String getAliRoyaltyType(Byte type) {
        String aliType = "";
        if (type.equals(RoyaltyRelationTypeEnum.USERID.getType())) {
            aliType = "userId";
        } else if (type.equals(RoyaltyRelationTypeEnum.LOGIN_NAME.getType())) {
            aliType = "loginName";
        } else if (type.equals(RoyaltyRelationTypeEnum.OPENID.getType())) {
            aliType = "openId";
        }
        return aliType;
    }

    @NotNull
    @Override
    @WithSpan("[alipay]分账结算")
    public OrderSettleResult orderSettle(Long merchantId, OrderSettleParam orderSettleParam) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);

            AlipayTradeOrderSettleRequest request = new AlipayTradeOrderSettleRequest();
            AlipayTradeOrderSettleModel model = new AlipayTradeOrderSettleModel();

            // 设置结算请求流水号
            model.setOutRequestNo(orderSettleParam.getRequestNo());

            // 设置支付宝订单号
            model.setTradeNo(orderSettleParam.getThirdSouceNo());

            // 设置分账明细信息
            List<OpenApiRoyaltyDetailInfoPojo> royaltyParameters = new ArrayList<>();
            OpenApiRoyaltyDetailInfoPojo royaltyParameters0 = new OpenApiRoyaltyDetailInfoPojo();
            royaltyParameters0.setRoyaltyType("transfer");
            royaltyParameters0.setTransInType(getAliRoyaltyType(orderSettleParam.getTransInType()));
            royaltyParameters0.setTransIn(orderSettleParam.getTransIn());
            royaltyParameters0.setAmount(AmountUtil.amount2BigDecimal(orderSettleParam.getAmount()).toString());
            royaltyParameters0.setDesc("商家分账");
            royaltyParameters.add(royaltyParameters0);
            model.setRoyaltyParameters(royaltyParameters);

            request.setBizModel(model);
            spanEnhancer.withJson("request", request);
            AlipayTradeOrderSettleResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);

            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return OrderSettleResult.failedResult(response.getSubCode());
            }
            return OrderSettleResult.builder()
                    .success(true)
                    .settleNo(response.getSettleNo())
                    .build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return OrderSettleResult.failedResult();
        }
    }

    @NotNull
    @Override
    @WithSpan("[alipay]关闭交易单")
    public ClosePayResult closePay(ClosePayParam closePayParam) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);
            spanEnhancer.withJson("参数", closePayParam);
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            JSONObject bizContent = new JSONObject();
            bizContent.put("out_trade_no", closePayParam.getPayNo());
            request.setBizContent(bizContent.toString());
            spanEnhancer.withJson("request", request.getBizContent());
            AlipayTradeCloseResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response.getBody());
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                // 订单不存在也返回成功关闭
                if (ThirdAliPayEnum.ClosePayCode.TRADE_NOT_EXIST.getCode().equals(response.getSubCode())) {
                    return ClosePayResult.builder().success(true).build();
                } else {
                    TradeQueryParam param = TradeQueryParam.builder()
                            .thirdPaySource(closePayParam.getThirdPaySource())
                            .payNo(closePayParam.getPayNo())
                            .build();
                    TradeQueryResult tradeQueryResult = this.tradeQuery(param);
                    if (tradeQueryResult.getSuccess()) {
                        if (tradeQueryResult.getTradeStatus().equals(ThirdAliPayEnum.TradeStatus.TRADE_CLOSED.getCode())) {
                            return ClosePayResult.builder().success(true).build();
                        }
                    }
                }
                return ClosePayResult.failedResult(response.getSubCode());
            }

            return ClosePayResult.builder().success(true).build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return ClosePayResult.failedResult();
        }
    }

    @NotNull
    @Override
    @WithSpan("[alipay]查询分账比例")
    public void royaltyRateQuery() {
        Span span = Span.current();
        try {
            AlipayTradeRoyaltyRateQueryRequest request = new AlipayTradeRoyaltyRateQueryRequest();
            AlipayTradeRoyaltyRateQueryModel model = new AlipayTradeRoyaltyRateQueryModel();
            model.setOutRequestNo(UuidUtil.getUUID());
            request.setBizModel(model);
            span.setAttribute("request", request.getBizContent());
            AlipayTradeRoyaltyRateQueryResponse response = aliPayPayClient.certificateExecute(request);
            span.setAttribute("response", response.getBody());
        } catch (AlipayApiException e) {
            span.recordException(e);
        }
    }

    @NotNull
    @Override
    @WithSpan("[alipay]统一收单交易查询")
    public TradeQueryResult tradeQuery(TradeQueryParam param) {
        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);

            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            AlipayTradeQueryModel model = new AlipayTradeQueryModel();

            // 设置订单支付时传入的商户订单号
            model.setOutTradeNo(param.getPayNo());

            // 设置查询选项
            List<String> queryOptions = new ArrayList<String>();
            queryOptions.add("trade_settle_info");
            model.setQueryOptions(queryOptions);

            request.setBizModel(model);

            spanEnhancer.withJson("request", request);
            AlipayTradeQueryResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return TradeQueryResult.failedResult(response.getSubCode());
            }

            List<TradeQueryResult.SettleDetail> settleDetailList = new ArrayList<>();
            if (response.getTradeSettleInfo() != null && response.getTradeSettleInfo().getTradeSettleDetailList() != null) {
                for (TradeSettleDetail tradeSettleDetail : response.getTradeSettleInfo().getTradeSettleDetailList()) {
                    TradeQueryResult.SettleDetail settleDetail = new TradeQueryResult.SettleDetail();
                    settleDetail.setAmount(AmountUtil.amount2Long(tradeSettleDetail.getAmount()));
                    settleDetail.setOperationSerialNo(tradeSettleDetail.getOperationSerialNo());
                    settleDetailList.add(settleDetail);
                }
            }

            return TradeQueryResult.builder().success(true).tradeStatus(response.getTradeStatus()).settleDetailList(settleDetailList).build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return TradeQueryResult.failedResult();
        }
    }

    @NotNull
    @Override
    @WithSpan("[alipay]统一收单交易退款查询")
    public RefundQueryResult refundQuery(RefundQueryParam param) {

        Span span = Span.current();
        try {
            SpanEnhancer spanEnhancer = SpanEnhancer.of(span);

            AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
            AlipayTradeFastpayRefundQueryModel model = new AlipayTradeFastpayRefundQueryModel();

            // 设置商户订单号
            model.setOutTradeNo(param.getPayNo());

            // 设置退款请求号
            model.setOutRequestNo(param.getRefundNo());

            // 设置查询选项
            List<String> queryOptions = new ArrayList<String>();
            queryOptions.add("refund_detail_item_list");
            queryOptions.add("gmt_refund_pay");
            model.setQueryOptions(queryOptions);

            request.setBizModel(model);

            spanEnhancer.withJson("request", request);
            AlipayTradeFastpayRefundQueryResponse response = aliPayPayClient.certificateExecute(request);
            spanEnhancer.withJson("response", response);
            if (!response.isSuccess()) {
                // sdk版本是"4.38.0.ALL"及以上,可以获取诊断链接
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                spanEnhancer.withString("诊断链接", diagnosisUrl);
                return RefundQueryResult.failedResult(response.getSubCode());
            }

            return RefundQueryResult.builder().success(true)
                    .fundChange("REFUND_SUCCESS".equals(response.getRefundStatus()))
                    .gmtRefundPay(response.getGmtRefundPay().getTime())
                    .refundAmount(AmountUtil.amount2Long(response.getRefundAmount())).build();
        } catch (AlipayApiException e) {
            span.recordException(e);
            return RefundQueryResult.failedResult();
        }
    }

    @NotNull
    @Override
    public Integer thirdPaySource() {
        return ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource();
    }
}
