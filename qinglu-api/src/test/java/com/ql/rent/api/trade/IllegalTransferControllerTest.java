package com.ql.rent.api.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.common.ITokenService;
import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.trade.IThirdIllegalTransferService;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.vehicle.StockOccupyVO;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IllegalTransferController单元测试
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ExtendWith(MockitoExtension.class)
class IllegalTransferControllerTest {

    @Mock
    private ITokenService tokenService;
    
    @Mock
    private IThirdIllegalTransferService thirdIllegalTransferService;
    
    @Mock
    private IVehicleBusyService vehicleBusyService;
    
    @Mock
    private IVehicleInfoService vehicleInfoService;

    @InjectMocks
    private IllegalTransferController illegalTransferController;

    private MockHttpServletRequest mockRequest;
    private LoginVo mockLoginVo;

    @BeforeEach
    void setUp() {
        mockRequest = new MockHttpServletRequest();
        mockLoginVo = new LoginVo();
        mockLoginVo.setMerchantId(1001L);
        mockLoginVo.setUserId(2001L);
    }

    @Test
    void testSubmitContract_Success() {
        // 准备测试数据
        SubmitContractRequest request = new SubmitContractRequest();
        request.setOrderId(12345L);
        request.setBeginTime(new Date());
        request.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        request.setVehicleId(67891L);

        SubmitContractResponse expectedResponse = new SubmitContractResponse();
        expectedResponse.setContractId("CT202401150001");
        expectedResponse.setContractNo("HT202401150001");
        expectedResponse.setStatus(1);
        expectedResponse.setPostCode("0000");

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);
        when(thirdIllegalTransferService.submitContract(any(), eq(1001L)))
            .thenReturn(ResultUtil.successResult(expectedResponse));

        // 执行测试
        var result = illegalTransferController.submitContract(request, mockRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(expectedResponse, result.getModel());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(thirdIllegalTransferService).submitContract(any(), eq(1001L));
    }

    @Test
    void testSubmitContract_UserNotLogin() {
        // 准备测试数据
        SubmitContractRequest request = new SubmitContractRequest();
        request.setOrderId(12345L);
        request.setBeginTime(new Date());
        request.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(null);

        // 执行测试
        var result = illegalTransferController.submitContract(request, mockRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("用户未登录", result.getMessage());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(thirdIllegalTransferService, never()).submitContract(any(), any());
    }

    @Test
    void testSubmitContract_InvalidRequest() {
        // 准备测试数据 - 缺少订单ID
        SubmitContractRequest request = new SubmitContractRequest();
        request.setBeginTime(new Date());
        request.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);

        // 执行测试
        var result = illegalTransferController.submitContract(request, mockRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("订单ID不能为空", result.getMessage());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(thirdIllegalTransferService, never()).submitContract(any(), any());
    }

    @Test
    void testSubmitContract_InvalidTimeRange() {
        // 准备测试数据 - 开始时间晚于结束时间
        SubmitContractRequest request = new SubmitContractRequest();
        request.setOrderId(12345L);
        request.setBeginTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        request.setEndTime(new Date());

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);

        // 执行测试
        var result = illegalTransferController.submitContract(request, mockRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("合同开始时间不能晚于结束时间", result.getMessage());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(thirdIllegalTransferService, never()).submitContract(any(), any());
    }

    @Test
    void testGetContractList_Success() {
        // 准备测试数据
        ContractListRequest request = new ContractListRequest();
        request.setOrderId(12345L);
        request.setPageNum(1);
        request.setPageSize(20);

        List<ContractListResponse> expectedList = new ArrayList<>();
        ContractListResponse response = new ContractListResponse();
        response.setId(1L);
        response.setOrderId(12345L);
        response.setContractId("CT202401150001");
        response.setStatus(1);
        expectedList.add(response);

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);
        when(thirdIllegalTransferService.contractList(any()))
            .thenReturn(ResultUtil.successResult(expectedList));

        // 执行测试
        var result = illegalTransferController.getContractList(request, mockRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals(1, result.getModel().size());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(thirdIllegalTransferService).contractList(any());
    }

    @Test
    void testCheckVehicleConflict_Success_NoConflict() {
        // 准备测试数据
        VehicleConflictCheckRequest request = new VehicleConflictCheckRequest();
        request.setOrderId(12345L);
        request.setTargetLicense("京A12345");

        VehicleInfoVO vehicleInfo = new VehicleInfoVO();
        vehicleInfo.setId(67891L);
        vehicleInfo.setLicense("京A12345");

        StockOccupyVO stockOccupyVO = new StockOccupyVO();
        stockOccupyVO.setBusyList(new ArrayList<>());

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);
        when(vehicleInfoService.vehicleInfoByLicense(1001L, "京A12345"))
            .thenReturn(ResultUtil.successResult(vehicleInfo));
        when(vehicleBusyService.stockOverForWk(any(), eq(1001L)))
            .thenReturn(ResultUtil.successResult(stockOccupyVO));

        // 执行测试
        var result = illegalTransferController.checkVehicleConflict(request, mockRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertFalse(result.getModel().getHasConflict());
        assertEquals(vehicleInfo, result.getModel().getTargetVehicleInfo());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(vehicleInfoService).vehicleInfoByLicense(1001L, "京A12345");
        verify(vehicleBusyService).stockOverForWk(any(), eq(1001L));
    }

    @Test
    void testCheckVehicleConflict_VehicleNotFound() {
        // 准备测试数据
        VehicleConflictCheckRequest request = new VehicleConflictCheckRequest();
        request.setOrderId(12345L);
        request.setTargetLicense("京A12345");

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);
        when(vehicleInfoService.vehicleInfoByLicense(1001L, "京A12345"))
            .thenReturn(ResultUtil.failResult("车辆不存在"));

        // 执行测试
        var result = illegalTransferController.checkVehicleConflict(request, mockRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertTrue(result.getModel().getHasConflict());
        assertNull(result.getModel().getTargetVehicleInfo());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(vehicleInfoService).vehicleInfoByLicense(1001L, "京A12345");
        verify(vehicleBusyService, never()).stockOverForWk(any(), any());
    }

    @Test
    void testCheckVehicleConflict_InvalidRequest() {
        // 准备测试数据 - 缺少车牌号
        VehicleConflictCheckRequest request = new VehicleConflictCheckRequest();
        request.setOrderId(12345L);

        // Mock依赖
        when(tokenService.getUserByRequest(mockRequest)).thenReturn(mockLoginVo);

        // 执行测试
        var result = illegalTransferController.checkVehicleConflict(request, mockRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("目标车牌不能为空", result.getMessage());
        
        // 验证Mock调用
        verify(tokenService).getUserByRequest(mockRequest);
        verify(vehicleInfoService, never()).vehicleInfoByLicense(any(), any());
        verify(vehicleBusyService, never()).stockOverForWk(any(), any());
    }
}
