package com.ql.rent.api.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.common.ITokenService;
import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.trade.IThirdIllegalTransferService;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 违章转移相关接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/illegal_transfer")
@Api(tags = "违章转移相关接口")
public class IllegalTransferController {

    @Resource
    private ITokenService tokenService;
    
    @Resource
    private IThirdIllegalTransferService thirdIllegalTransferService;
    
    @Resource
    private IVehicleBusyService vehicleBusyService;
    
    @Resource
    private IVehicleInfoService vehicleInfoService;

    /**
     * 手动上报合同
     */
    @ApiOperation(value = "手动上报合同", notes = "手动上报违章转移合同，新增一个合同记录")
    @PostMapping("/v1/contract/submit")
    public ResultMap<SubmitContractResponse> submitContract(
            @RequestBody SubmitContractRequest request,
            HttpServletRequest httpRequest) {
        try {
            log.info("手动上报违章转移合同, request: {}", JSON.toJSONString(request));
            
            // 获取登录用户信息
            LoginVo loginVo = tokenService.getUserByRequest(httpRequest);
            if (loginVo == null) {
                return ApiResultUtil.failResult("用户未登录");
            }
            
            // 参数校验
            if (request == null || request.getOrderId() == null) {
                return ApiResultUtil.failResult("订单ID不能为空");
            }
            
            if (request.getBeginTime() == null || request.getEndTime() == null) {
                return ApiResultUtil.failResult("合同开始时间和结束时间不能为空");
            }
            
            if (request.getBeginTime().after(request.getEndTime())) {
                return ApiResultUtil.failResult("合同开始时间不能晚于结束时间");
            }
            
            // 调用服务层处理
            Result<com.ql.rent.remote.vehicle.vo.response.SubmitContractResponse> result = thirdIllegalTransferService.submitContract(
                    buildSubmitContractReq(request), loginVo.getMerchantId());
            
            if (ResultUtil.isResultNotSuccess(result)) {
                log.error("手动上报违章转移合同失败, request: {}, error: {}", 
                         JSON.toJSONString(request), result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            
            log.info("手动上报违章转移合同成功, request: {}, response: {}", 
                     JSON.toJSONString(request), JSON.toJSONString(result.getModel()));
            return ApiResultUtil.successResult(result.getModel());
            
        } catch (Exception e) {
            log.error("手动上报违章转移合同异常, request: {}", JSON.toJSONString(request), e);
            return ApiResultUtil.failResult("系统异常: " + e.getMessage());
        }
    }

    /**
     * 查询合同列表
     */
    @ApiOperation(value = "查询合同列表", notes = "根据查询条件获取违章转移合同列表")
    @PostMapping("/v1/contract/list")
    public ResultMap<List<TransferContractStatusVo>> getContractList(
            @RequestBody ContractListRequest request,
            HttpServletRequest httpRequest) {
        try {
            log.info("查询违章转移合同列表, request: {}", JSON.toJSONString(request));
            
            // 获取登录用户信息
            LoginVo loginVo = tokenService.getUserByRequest(httpRequest);
            if (loginVo == null) {
                return ApiResultUtil.failResult("用户未登录");
            }
            
            // 设置商家ID
            request.setMerchantId(loginVo.getMerchantId());
            
            // 调用服务层处理
            Result<List<com.ql.rent.remote.vehicle.vo.response.ContractListResponse>> result = thirdIllegalTransferService.contractList(
                    buildContractListReq(request));
            
            if (ResultUtil.isResultNotSuccess(result)) {
                log.error("查询违章转移合同列表失败, request: {}, error: {}", 
                         JSON.toJSONString(request), result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            
            // 转换响应数据
            List<TransferContractStatusVo> responseList = convertToTransferContractStatusVoList(result.getModel());
            
            log.info("查询违章转移合同列表成功, request: {}, count: {}", 
                     JSON.toJSONString(request), responseList.size());
            return ApiResultUtil.successResult(responseList);
            
        } catch (Exception e) {
            log.error("查询违章转移合同列表异常, request: {}", JSON.toJSONString(request), e);
            return ApiResultUtil.failResult("系统异常: " + e.getMessage());
        }
    }

    /**
     * 检查车辆改排后库存冲突
     */
    @ApiOperation(value = "检查车辆改排后库存冲突", notes = "取车自动改排逻辑：判断车辆改排后是否与其他库存冲突")
    @PostMapping("/v1/check_vehicle_conflict")
    public ResultMap<VehicleConflictCheckResponse> checkVehicleConflict(
            @RequestBody VehicleConflictCheckRequest request,
            HttpServletRequest httpRequest) {
        try {
            log.info("检查车辆改排后库存冲突, request: {}", JSON.toJSONString(request));
            
            // 获取登录用户信息
            LoginVo loginVo = tokenService.getUserByRequest(httpRequest);
            if (loginVo == null) {
                return ApiResultUtil.failResult("用户未登录");
            }
            
            // 参数校验
            if (request == null || request.getOrderId() == null) {
                return ApiResultUtil.failResult("订单ID不能为空");
            }
            
            if (request.getTargetLicense() == null || request.getTargetLicense().trim().isEmpty()) {
                return ApiResultUtil.failResult("目标车牌不能为空");
            }
            
            // 查询目标车辆信息
            Result<VehicleInfoVO> vehicleResult = vehicleInfoService.vehicleInfoByLicense(
                    loginVo.getMerchantId(), request.getTargetLicense());
            
            VehicleConflictCheckResponse response = new VehicleConflictCheckResponse();
            
            if (ResultUtil.isResultNotSuccess(vehicleResult) || vehicleResult.getModel() == null) {
                // 车辆不存在或不属于该商家
                response.setHasConflict(true);
                response.setTargetVehicleInfo(null);
                log.warn("目标车辆不存在或不属于该商家, license: {}, merchantId: {}", 
                        request.getTargetLicense(), loginVo.getMerchantId());
                return ApiResultUtil.successResult(response);
            }
            
            VehicleInfoVO vehicleInfo = vehicleResult.getModel();
            response.setTargetVehicleInfo(vehicleInfo);
            
            // 检查库存冲突
            boolean hasConflict = checkStockConflict(request.getOrderId(), vehicleInfo.getId(), loginVo.getMerchantId());
            response.setHasConflict(hasConflict);
            
            log.info("检查车辆改排后库存冲突完成, request: {}, hasConflict: {}", 
                     JSON.toJSONString(request), hasConflict);
            return ApiResultUtil.successResult(response);
            
        } catch (Exception e) {
            log.error("检查车辆改排后库存冲突异常, request: {}", JSON.toJSONString(request), e);
            return ApiResultUtil.failResult("系统异常: " + e.getMessage());
        }
    }

    /**
     * 构建提交合同请求参数
     */
    private com.ql.rent.remote.vehicle.vo.request.SubmitContractReq buildSubmitContractReq(SubmitContractRequest request) {
        com.ql.rent.remote.vehicle.vo.request.SubmitContractReq req = new com.ql.rent.remote.vehicle.vo.request.SubmitContractReq();
        req.setBeginTime(request.getBeginTime());
        req.setEndTime(request.getEndTime());
        // 设置其他必要字段，这里需要根据实际业务逻辑填充
        return req;
    }

    /**
     * 构建合同列表查询请求参数
     */
    private com.ql.rent.remote.vehicle.vo.request.ContractListReq buildContractListReq(ContractListRequest request) {
        com.ql.rent.remote.vehicle.vo.request.ContractListReq req = new com.ql.rent.remote.vehicle.vo.request.ContractListReq();
        req.setBeginTime(request.getStartBeginTime());
        req.setEndTime(request.getEndBeginTime());
        // 设置其他必要字段，这里需要根据实际业务逻辑填充
        return req;
    }

    /**
     * 转换合同列表响应数据
     */
    private List<TransferContractStatusVo> convertToTransferContractStatusVoList(List<com.ql.rent.remote.vehicle.vo.response.ContractListResponse> responseList) {
        if (responseList == null || responseList.isEmpty()) {
            return new ArrayList<>();
        }

        List<TransferContractStatusVo> result = new ArrayList<>();
        for (com.ql.rent.remote.vehicle.vo.response.ContractListResponse response : responseList) {
            TransferContractStatusVo vo = new TransferContractStatusVo();
            vo.setDriverName(response.getDriverName());
            vo.setDriverPhone(response.getDriverPhone());
            vo.setDriverId(response.getDriverId());
            vo.setAccount(response.getAccount());
            vo.setCarNumber(response.getCarNumber());
            vo.setContractNo(response.getContractNo());
            vo.setBeginTime(response.getBeginTime());
            vo.setEndTime(response.getEndTime());
            vo.setCarType(response.getCarType());
            vo.setOrderId(response.getOrderId());
            vo.setStatus(response.getState() != null ? response.getState().byteValue() : null);
            result.add(vo);
        }
        return result;
    }

    /**
     * 检查库存冲突
     */
    private boolean checkStockConflict(Long orderId, Long vehicleId, Long merchantId) {
        try {
            // 简化处理：这里应该根据实际业务逻辑来检查库存冲突
            // 可以调用相关的库存服务来检查车辆在指定时间段内是否被占用

            // 暂时返回false，表示没有冲突
            // 实际实现中应该调用具体的库存检查服务
            return false;
        } catch (Exception e) {
            log.error("检查库存冲突异常, orderId: {}, vehicleId: {}, merchantId: {}",
                     orderId, vehicleId, merchantId, e);
            // 异常情况下为了安全起见返回有冲突
            return true;
        }
    }
}
